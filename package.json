{"name": "my-tubo", "private": true, "scripts": {"build": "turbo run build", "build-frontend": "turbo run build --filter=frontend", "build-backend": "turbo run build --filter=backend", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "prepare": "husky"}, "devDependencies": {"lint-staged": "^16.1.2", "prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "lint-staged": {"*.{ts,tsx,md}": "prettier --write"}, "dependencies": {"husky": "^9.1.7"}}