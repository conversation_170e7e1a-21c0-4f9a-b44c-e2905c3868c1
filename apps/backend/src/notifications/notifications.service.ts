import { Injectable } from '@nestjs/common';
import { ENGAGESPOT_WORKFLOW } from 'src/engagespot/enagagespot.workflows';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class NotificationsService {
  constructor(
    private readonly engagespotService: EngagespotService,
    private readonly prismaService: PrismaService,
  ) {}

  async sendEmailVerificationNotification(
    userId: string,
    otp: string,
    expirationTime: string,
  ) {
    const user = await this.prismaService.user.findUnique({
      where: {
        id: userId,
      },
    });

    const payload = {
      username: user.fullName,
      otp,
      expirationTime,
    };
    return this.engagespotService.send(
      ENGAGESPOT_WORKFLOW.EMAIL_VERIFICATION,
      [userId],
      payload,
    );
  }

  async sendForgotPasswordNotification(
    userId: string,
    token: string,
    expirationTime: string,
  ) {
    const user = await this.prismaService.user.findUnique({
      where: {
        id: userId,
      },
    });

    const payload = {
      username: user.fullName,
      resetPasswordLink: `${process.env.APP_URL}/auth/reset-password?token=${token}`,
      expirationTime,
    };
    return this.engagespotService.send(
      ENGAGESPOT_WORKFLOW.FORGOT_PASSWORD,
      [userId],
      payload,
    );
  }
}
