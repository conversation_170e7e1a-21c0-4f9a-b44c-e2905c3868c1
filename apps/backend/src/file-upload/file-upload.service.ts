import {
  PutObjectCommand,
  S3Client,
  S3ServiceException,
} from '@aws-sdk/client-s3';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AccessType, FileType } from '../../prisma/app/generated/prisma/client';
import { extname } from 'path';
import { PrismaService } from 'src/prisma/prisma.service';
import { uuid } from 'uuidv4';

@Injectable()
export class FileUploadService {
  private readonly fileExtensionMappings = {
    // Image extensions
    jpg: FileType.image,
    jpeg: FileType.image,
    png: FileType.image,
    gif: FileType.image,
    webp: FileType.image,
    svg: FileType.image,

    // Video extensions
    mp4: FileType.video,
    mov: FileType.video,
    avi: FileType.video,
    mkv: FileType.video,
    webm: FileType.video,

    // Audio extensions
    mp3: FileType.audio,
    wav: FileType.audio,
    ogg: FileType.audio,
    m4a: FileType.audio,

    // Document extensions
    pdf: FileType.document,
    doc: FileType.document,
    docx: FileType.document,
    txt: FileType.document,
    xls: FileType.document,
    xlsx: FileType.document,
    ppt: FileType.document,
    pptx: FileType.document,
    csv: FileType.document,
  };

  constructor(
    private readonly prismaService: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  private getFileExtension(fileName: string): string {
    return extname(fileName).slice(1).toLowerCase();
  }

  private validateFileType(fileName: string): FileType {
    const extension = this.getFileExtension(fileName);
    const fileType = this.fileExtensionMappings[extension];

    if (!fileType) {
      throw new BadRequestException(`Unsupported file extension: ${extension}`);
    }

    return fileType;
  }

  async uploadPublicFile(file: Buffer, fileName: string, uploaderId: string) {
    try {
      const fileType = this.validateFileType(fileName);

      const mimeTypeMap = {
        [FileType.image]: 'image/jpeg',
        [FileType.video]: 'video/mp4',
        [FileType.audio]: 'audio/mpeg',
        [FileType.document]: this.getMimeTypeForDocument(fileName),
      };
      const contentType = mimeTypeMap[fileType] || 'application/octet-stream';
      const s3Client = this.getS3Client();
      const key = `${fileType}/${uuid()}-fname-${fileName}`;
      const url = `https://${this.getPublicBucketName()}.s3.${this.getAwsRegion()}.amazonaws.com/${key}`;

      const uploadCommand = new PutObjectCommand({
        Bucket: this.getPublicBucketName(),
        Body: file,
        Key: key,
        ContentType: contentType,
        ContentDisposition: 'inline',
      });
      await s3Client.send(uploadCommand);

      const fileRecord = await this.prismaService.file.create({
        data: {
          key,
          path: url,
          file_type: fileType,
          access_type: AccessType.public,
          userId: uploaderId,
        },
      });

      return {
        id: fileRecord.id,
        file_name: fileName,
        key,
        file_url: url,
        file_type: fileType,
        access_type: fileRecord.access_type,
      };
    } catch (error) {
      if (
        error instanceof S3ServiceException &&
        error.name === 'EntityTooLarge'
      ) {
        throw new BadRequestException(`Error from S3 while uploading object to bucket \
                  The object was too large. To upload objects larger than 5GB, use the S3 console (160GB max) \
                  or the multipart upload API (5TB max).`);
      } else if (error instanceof S3ServiceException) {
        throw new BadRequestException(
          `Error from S3 while uploading object to bucket.  ${error.name}: ${error.message}`,
        );
      } else {
        throw new BadRequestException(`${error.name} : ${error.message}`);
      }
    }
  }

  private getS3Client() {
    return new S3Client({
      region: this.getAwsRegion(),
      credentials: {
        accessKeyId: this.getAwsAccessKeyId(),
        secretAccessKey: this.getAwsSecretAccessKey(),
      },
    });
  }

  private getAwsAccessKeyId() {
    return this.configService.get<string>(
      'AWS_ACCESS_KEY_ID',
      '********************',
    );
  }

  private getAwsSecretAccessKey() {
    return this.configService.get<string>(
      'AWS_SECRET_ACCESS_KEY',
      'htqh22m4FntFYXF+rMwJOa09mYmx/9QDaJbS47a4',
    );
  }

  private getAwsRegion() {
    return this.configService.get<string>('AWS_REGION', 'eu-north-1');
  }

  private getPublicBucketName() {
    return this.configService.get<string>(
      'AWS_PUBLIC_BUCKET_NAME',
      'buildplanai-public-bucket',
    );
  }

  async deleteFile(id: number, authUserId: string) {
    const file = await this.prismaService.file.findUnique({
      where: { id, deleted_at: null },
    });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    if (authUserId !== file.userId) {
      throw new ForbiddenException(
        'You do not have permission to delete this file.',
      );
    }
    await this.prismaService.file.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });

    return {
      message: 'File successfully deleted',
    };
  }
  // Helper to get MIME type for document files
  private getMimeTypeForDocument(fileName: string): string {
    const ext = this.getFileExtension(fileName);
    switch (ext) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'csv':
        return 'text/csv';
      case 'txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }
}
