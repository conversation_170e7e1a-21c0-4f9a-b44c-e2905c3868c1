import {
  Controller,
  Delete,
  HttpStatus,
  Param,
  ParseFilePipeBuilder,
  Post,
  Req,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('file')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}
  @Post('upload/aws')
  @UseInterceptors(FileInterceptor('file'))
  uploadFileToAwsS3(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addMaxSizeValidator({
          maxSize: 1024 * 1024 * 50, //In bytes
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    file: Express.Multer.File,
    @Req() { user },
  ) {
    const fileName = file.originalname;
    const uploaderId = user.id;
    return this.fileUploadService.uploadPublicFile(
      file.buffer,
      fileName,
      uploaderId,
    );
  }

  @Delete(':id')
  delete(@Param('id') id: number, @Req() { user }) {
    return this.fileUploadService.deleteFile(id, user.id);
  }
}
