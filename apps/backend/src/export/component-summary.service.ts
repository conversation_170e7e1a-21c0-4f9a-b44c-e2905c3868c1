import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  generateComponentSummary,
  calculateProjectTotals,
  ComponentSummaryData,
  Component,
  Drawing,
  ScaleInfo,
  PaperSize,
} from '@repo/component-summary';

export interface ComponentSummaryExport {
  takeoffInfo: {
    id: number;
    name: string;
    exportType: 'current-page' | 'aggregate';
    blueprintImageId?: string;
    exportedAt: string;
  };
  scale: ScaleInfo;
  paperSize: PaperSize;
  components: ComponentSummaryData[];
  totals: {
    totalSurfaceArea: any;
    totalEdgeLength: any;
    totalPointCount: any;
  };
}

@Injectable()
export class ComponentSummaryService {
  constructor(private prisma: PrismaService) {}

  async generateExportData(
    takeoffId: number,
    exportType: 'current-page' | 'aggregate',
    blueprintImageId?: string,
  ): Promise<ComponentSummaryExport> {
    const { components, drawings, takeoffInfo } = await this.getTakeoffData(
      takeoffId,
      blueprintImageId,
      exportType,
    );

    // Use shared package functions for consistent calculations
    const summaryData = generateComponentSummary(
      components,
      drawings,
      takeoffInfo.scale,
      takeoffInfo.paperSize,
    );

    const totals = calculateProjectTotals(summaryData);

    return {
      takeoffInfo: {
        id: takeoffInfo.id,
        name: takeoffInfo.name,
        exportType,
        blueprintImageId,
        exportedAt: new Date().toISOString(),
      },
      scale: takeoffInfo.scale,
      paperSize: takeoffInfo.paperSize,
      components: Array.from(summaryData.values()),
      totals,
    };
  }

  private async getTakeoffData(
    takeoffId: number,
    blueprintImageId?: string,
    exportType?: string,
  ) {
    // Fetch takeoff data with error handling
    const takeoff = await this.prisma.takeoff.findUnique({
      where: { id: takeoffId },
      include: {
        blueprintFiles: {
          include: {
            blueprintImages: true,
          },
        },
      },
    });

    if (!takeoff) {
      throw new NotFoundException(`Takeoff with ID ${takeoffId} not found`);
    }

    // Get the first blueprint file (assuming one per takeoff for now)
    const blueprintFile = takeoff.blueprintFiles[0];
    if (!blueprintFile) {
      throw new NotFoundException(
        `No blueprint file found for takeoff ${takeoffId}`,
      );
    }

    // Build component filter based on export type
    const componentFilter: any = {
      blueprintFileId: blueprintFile.id,
      deletedAt: null,
    };

    if (exportType === 'current-page' && blueprintImageId) {
      // For current page, include allPages components and currentPage components for the specific image
      componentFilter.OR = [
        { selectionType: 'allPages' },
        {
          selectionType: 'currentPage',
          blueprintImages: {
            some: {
              id: blueprintImageId,
            },
          },
        },
      ];
    }

    // Fetch components
    const components = await this.prisma.component.findMany({
      where: componentFilter,
      include: {
        blueprintImages: true,
      },
    });

    // Build drawing filter
    const drawingFilter: any = {
      componentId: { in: components.map((c) => c.id) },
      deletedAt: null,
    };

    if (exportType === 'current-page' && blueprintImageId) {
      drawingFilter.blueprintImageId = blueprintImageId;
    }

    // Fetch drawings
    const drawings = await this.prisma.drawing.findMany({
      where: drawingFilter,
    });

    // Get scale and paper size from the first blueprint image
    const firstImage = blueprintFile.blueprintImages[0];
    const scale = firstImage?.scale as any;
    const dimensions = firstImage?.dimensions as any;

    return {
      components: components.map(this.transformComponent),
      drawings: drawings.map(this.transformDrawing),
      takeoffInfo: {
        id: takeoff.id,
        name: takeoff.name || 'Untitled Takeoff',
        scale: this.transformScale(scale),
        paperSize: this.transformPaperSize(dimensions),
      },
    };
  }

  private transformComponent(dbComponent: any): Component {
    return {
      id: dbComponent.id,
      name: dbComponent.name,
      geometryType: dbComponent.geometryType,
      selectionType: dbComponent.selectionType,
      blueprintFileId: dbComponent.blueprintFileId,
      blueprintImageId: dbComponent.blueprintImages?.[0]?.id,
      color: dbComponent.color || '#000000',
      shade: dbComponent.shade || '500',
      description: dbComponent.description,
    };
  }

  private transformDrawing(dbDrawing: any): Drawing {
    return {
      id: dbDrawing.id,
      componentId: dbDrawing.componentId,
      blueprintImageId: dbDrawing.blueprintImageId,
      config: dbDrawing.config || {},
      createdAt: dbDrawing.createdAt.toISOString(),
      updatedAt: dbDrawing.updatedAt.toISOString(),
      deletedAt: dbDrawing.deletedAt?.toISOString() || null,
    };
  }

  private transformScale(dbScale: any): ScaleInfo {
    // Handle the case where scale might not be set
    if (!dbScale) {
      return {
        num_metric: 1,
        num_unit: 'inch',
        den_metric: 1,
        den_unit: 'ft',
      };
    }

    return {
      num_metric: dbScale.num_metric || 1,
      num_unit: dbScale.num_unit || 'inch',
      den_metric: dbScale.den_metric || 1,
      den_unit: dbScale.den_unit || 'ft',
    };
  }

  private transformPaperSize(dbDimensions: any): PaperSize {
    // Handle the case where dimensions might not be set
    if (!dbDimensions) {
      return {
        width: 8.5,
        height: 11,
        unit: 'inch',
      };
    }

    return {
      width: dbDimensions.width || 8.5,
      height: dbDimensions.height || 11,
      unit: dbDimensions.unit || 'inch',
    };
  }
}
