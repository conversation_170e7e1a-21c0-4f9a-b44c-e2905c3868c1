import { Injectable } from '@nestjs/common';
import { Response } from 'express';
import * as ExcelJS from 'exceljs';
import {
  ComponentSummaryService,
  ComponentSummaryExport,
} from './component-summary.service';
import { formatMeasurement } from '@repo/component-summary';

@Injectable()
export class ExportService {
  constructor(private componentSummaryService: ComponentSummaryService) {}

  async exportToExcel(
    takeoffId: number,
    exportType: 'current-page' | 'aggregate',
    blueprintImageId?: string,
    res?: Response,
  ): Promise<ExcelJS.Workbook> {
    // Get component summary data using shared package calculations
    const summaryData = await this.componentSummaryService.generateExportData(
      takeoffId,
      exportType,
      blueprintImageId,
    );

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();

    // Set workbook properties
    workbook.creator = 'Takeoff Application';
    workbook.created = new Date();
    workbook.modified = new Date();

    // Add project info sheet
    await this.createProjectInfoSheet(workbook, summaryData);

    // Add component summary sheet
    await this.createComponentSummarySheet(workbook, summaryData);

    if (res) {
      // Set headers for file download
      const fileName = this.generateFileName(summaryData);
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader('Content-Disposition', `attachment;filename=${fileName}`);

      await workbook.xlsx.write(res);
      res.end();
    }

    return workbook;
  }

  private async createProjectInfoSheet(
    workbook: ExcelJS.Workbook,
    data: ComponentSummaryExport,
  ): Promise<void> {
    const worksheet = workbook.addWorksheet('Project Info');

    // Set column widths
    worksheet.columns = [{ width: 25 }, { width: 40 }];

    // Project metadata section
    worksheet.addRow(['Project Information', '']);
    worksheet.addRow(['Project Name', data.takeoffInfo.name]);
    worksheet.addRow([
      'Export Type',
      data.takeoffInfo.exportType === 'current-page'
        ? 'Current Page'
        : 'Aggregate (Full Project)',
    ]);
    worksheet.addRow([
      'Blueprint Image ID',
      data.takeoffInfo.blueprintImageId || 'All Pages',
    ]);
    worksheet.addRow([
      'Exported At',
      new Date(data.takeoffInfo.exportedAt).toLocaleString(),
    ]);
    worksheet.addRow(['']);

    // Scale information section
    worksheet.addRow(['Scale Information', '']);
    worksheet.addRow([
      'Scale',
      `${data.scale.num_metric} ${data.scale.num_unit} = ${data.scale.den_metric} ${data.scale.den_unit}`,
    ]);
    worksheet.addRow([
      'Paper Size',
      `${data.paperSize.width}" × ${data.paperSize.height}" (${data.paperSize.unit})`,
    ]);
    worksheet.addRow(['']);

    // Project totals section
    worksheet.addRow(['Project Totals', '']);

    // Format totals with fallback for missing formatMeasurement
    const formatTotal = (total: any) => {
      if (typeof formatMeasurement === 'function') {
        return formatMeasurement(
          total.value || total.total || 0,
          total.unit || '',
        );
      }
      return `${total.value || total.total || 0} ${total.unit || ''}`;
    };

    worksheet.addRow([
      'Total Surface Area',
      formatTotal(data.totals.totalSurfaceArea),
    ]);
    worksheet.addRow([
      'Total Edge Length',
      formatTotal(data.totals.totalEdgeLength),
    ]);
    worksheet.addRow([
      'Total Point Count',
      (
        data.totals.totalPointCount.value ||
        data.totals.totalPointCount.total ||
        0
      ).toString(),
    ]);

    // Style the headers
    this.styleHeaderRows(worksheet, [1, 7, 11]);
  }

  private async createComponentSummarySheet(
    workbook: ExcelJS.Workbook,
    data: ComponentSummaryExport,
  ): Promise<void> {
    const worksheet = workbook.addWorksheet('Component Summary');

    // Define columns with proper headers
    worksheet.columns = [
      { header: 'Component Name', key: 'name', width: 25 },
      { header: 'Type', key: 'type', width: 15 },
      { header: 'Selection Mode', key: 'selection', width: 15 },
      { header: 'Quantity/Total', key: 'total', width: 20 },
      { header: 'Unit', key: 'unit', width: 10 },
      { header: 'Drawing Count', key: 'drawingCount', width: 15 },
      { header: 'Description', key: 'description', width: 30 },
    ];

    // Add data rows
    data.components.forEach((component) => {
      const totalMetrics = component.totalMetrics;
      let quantity: number, unit: string;

      // Use geometry type to determine what to display
      if (component.componentType === 'surface') {
        quantity = totalMetrics.value || 0;
        unit = totalMetrics.unit || 'sq ft';
      } else if (component.componentType === 'edge') {
        quantity = totalMetrics.value || 0;
        unit = totalMetrics.unit || 'ft';
      } else {
        quantity = totalMetrics.value || 0;
        unit = 'count';
      }

      // Format the measurement
      const formattedTotal =
        totalMetrics.formattedValue || `${quantity} ${unit}`;

      worksheet.addRow({
        name: component.componentName,
        type: component.componentType,
        selection: 'N/A', // Selection type not available in ComponentSummaryData
        total: formattedTotal,
        unit: unit,
        drawingCount: component.drawingSummaryItems?.length || 0,
        description: '', // Description not available in ComponentSummaryData
      });
    });

    // Style the header row
    this.styleHeaderRow(worksheet, 1);

    // Add alternating row colors
    this.addAlternatingRowColors(worksheet, data.components.length + 1);
  }

  private styleHeaderRows(
    worksheet: ExcelJS.Worksheet,
    rowNumbers: number[],
  ): void {
    rowNumbers.forEach((rowNum) => {
      const row = worksheet.getRow(rowNum);
      row.eachCell((cell) => {
        cell.font = { bold: true, size: 12 };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFD0D0D0' },
        };
      });
    });
  }

  private styleHeaderRow(
    worksheet: ExcelJS.Worksheet,
    rowNumber: number,
  ): void {
    const headerRow = worksheet.getRow(rowNumber);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF366092' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
  }

  private addAlternatingRowColors(
    worksheet: ExcelJS.Worksheet,
    totalRows: number,
  ): void {
    for (let i = 2; i <= totalRows; i++) {
      if (i % 2 === 0) {
        const row = worksheet.getRow(i);
        row.eachCell((cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF8F8F8' },
          };
        });
      }
    }
  }

  private generateFileName(data: ComponentSummaryExport): string {
    const sanitizedName = data.takeoffInfo.name.replace(/[^a-zA-Z0-9]/g, '_');
    const exportType = data.takeoffInfo.exportType.replace('-', '_');
    const date = new Date().toISOString().split('T')[0];
    return `${sanitizedName}_${exportType}_${date}.xlsx`;
  }
}
