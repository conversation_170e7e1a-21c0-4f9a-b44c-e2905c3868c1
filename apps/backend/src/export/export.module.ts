import { Module } from '@nestjs/common';
import { ExportController } from './export.controller';
import { ExportService } from './export.service';
import { ComponentSummaryService } from './component-summary.service';
import { ComponentsModule } from '../components/components.module';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [ComponentsModule, PrismaModule],
  controllers: [ExportController],
  providers: [ExportService, ComponentSummaryService],
  exports: [ExportService, ComponentSummaryService],
})
export class ExportModule {}
