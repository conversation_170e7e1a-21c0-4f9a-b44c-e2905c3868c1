import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateUserVerificationDto } from './dto/create-user-verification.dto';
import { VerifyUserVerificationDto } from './dto/verify-user-verification.dto';
import { VerificationType } from '../../prisma/app/generated/prisma/client';
import { subMinutes } from 'date-fns';

@Injectable()
export class UserVerificationService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createUserVerificationDto: CreateUserVerificationDto) {
    return this.prisma.userVerification.create({
      data: {
        userId: createUserVerificationDto.userId,
        verificationType: createUserVerificationDto.verificationType,
        code: createUserVerificationDto.code,
        expiresAt:
          createUserVerificationDto.expiresAt ||
          new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
      },
    });
  }

  async verify(verifyUserVerificationDto: VerifyUserVerificationDto) {
    const userVerification = await this.prisma.userVerification.findFirst({
      where: {
        userId: verifyUserVerificationDto.userId,
        verificationType: verifyUserVerificationDto.verificationType,
        code: verifyUserVerificationDto.code,
      },
    });

    if (!userVerification) {
      throw new NotFoundException('Invalid verification code');
    }

    if (userVerification.expiresAt < new Date()) {
      throw new BadRequestException('Verification code has expired');
    }

    if (userVerification.verifiedAt) {
      throw new BadRequestException(
        'Verification code has already been verified',
      );
    }

    await this.prisma.userVerification.update({
      where: {
        id: userVerification.id,
      },
      data: {
        verifiedAt: new Date(),
      },
    });

    return userVerification;
  }

  async forceExpireUserVerifications(
    userId: string,
    verificationType: VerificationType,
  ) {
    await this.prisma.userVerification.updateMany({
      where: {
        userId,
        verificationType,
        verifiedAt: null,
        expiresAt: { gte: new Date() },
      },
      data: {
        expiresAt: subMinutes(new Date(), 1),
      },
    });
  }
}
