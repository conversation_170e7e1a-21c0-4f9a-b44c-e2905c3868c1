import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { PrismaModule } from './prisma/prisma.module';
import { RolesModule } from './roles/roles.module';
import { UserVerificationModule } from './user-verification/user-verification.module';
import { FileUploadModule } from './file-upload/file-upload.module';
import { TakeoffModule } from './takeoff/takeoff.module';
import { BlueprintFilesModule } from './blueprint-files/blueprint-files.module';
import { ComponentsModule } from './components/components.module';
import { DrawingsModule } from './drawings/drawings.module';
// import { ExportModule } from './export/export.module';
import { NotificationsModule } from './notifications/notifications.module';
import { EngagespotModule } from './engagespot/engagespot.module';
import { BullModule } from '@nestjs/bull';
import configuration from './configuration/configuration';
import { PaginationModule } from './pagination/pagination.module';
import { RolesGuard } from './roles/guards/roles.guard';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
      },
    }),
    AuthModule,
    UsersModule,
    PrismaModule,
    RolesModule,
    UserVerificationModule,
    FileUploadModule,
    TakeoffModule,
    BlueprintFilesModule,
    ComponentsModule,
    DrawingsModule,
    // ExportModule,
    NotificationsModule,
    EngagespotModule,
    PaginationModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
})
export class AppModule {}
