import { Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { FileUploadDto } from 'src/file-upload/dto/file-upload.dto';

export class ProfileUpdateDto {
  @IsOptional()
  @IsString()
  @MaxLength(255)
  fullName?: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  phoneNumber?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => FileUploadDto)
  profilePic?: any;
}
