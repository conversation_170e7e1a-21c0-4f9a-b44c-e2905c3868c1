import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  IsDateString,
  IsJSON,
  ValidateNested,
} from 'class-validator';
import { ROLE_NAMES } from 'src/roles/enum/roles.enum';
import { Type } from 'class-transformer';
import { FileUploadDto } from 'src/file-upload/dto/file-upload.dto';
import { UserStatus } from 'prisma/app/generated/prisma/client';
export class CreateUserDto {
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  fullName?: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password: string;

  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @IsString()
  @IsOptional()
  organizationName?: string;

  @IsString()
  @IsNotEmpty()
  @IsEnum(ROLE_NAMES)
  roleName: string;

  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @IsOptional()
  @IsDateString()
  phoneNumberVerifiedAt?: Date;

  @IsOptional()
  @IsDateString()
  emailVerifiedAt?: Date;

  @IsOptional()
  @IsString()
  resetToken?: string;

  @IsOptional()
  @IsDateString()
  resetTokenExpiresAt?: Date;

  @IsOptional()
  @ValidateNested()
  @Type(() => FileUploadDto)
  profilePic?: any;
}
