import {
  Controller,
  Get,
  Request,
  Post,
  Delete,
  Param,
  Patch,
  Body,
  Req,
  Query,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { Public } from 'src/auth/decorators/public.decorator';
import { Roles } from 'src/roles/decorators/roles.decorator';
import { ROLE_NAMES } from 'src/roles/enum/roles.enum';
import { ProfileUpdateDto } from './dto/profile-update.dto';
import { PaginateQuery } from 'src/pagination/pagination.query';
import { UpdateUserDto } from './dto/update-user.dto';
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('me')
  async getProfile(@Request() req) {
    const user = await this.usersService.findById(req.user.id);
    const { password, refreshToken, ...result } = user;
    return result;
  }

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Get()
  async findAll(@Query() query: PaginateQuery) {
    return this.usersService.findAll(query);
  }

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.usersService.findById(id);
  }

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return await this.usersService.update(id, updateUserDto);
  }

  @Patch('profile')
  async profile(@Req() { user }, @Body() profileUpdateDto: ProfileUpdateDto) {
    return await this.usersService.update(user.id, profileUpdateDto);
  }

  @Public()
  @Post('admin/seeder')
  async adminSeeder() {
    return this.usersService.adminSeeder();
  }

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Delete(':id')
  async remove(@Param('id') id: string) {
    return this.usersService.remove(id);
  }
}
