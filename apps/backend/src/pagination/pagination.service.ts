import { Injectable } from '@nestjs/common';
import { PaginatedResult } from './pagination.result';
import { PrismaClient } from '../../prisma/app/generated/prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
@Injectable()
export class PaginationService {
  constructor(private prismaService: PrismaService) {}

  async paginate<T>(
    model: keyof Omit<PrismaClient, symbol>,
    {
      page = 1,
      perPage = 10,
      where = {},
      include,
      orderBy = {},
      select,
    }: {
      page?: number;
      perPage?: number;
      where?: any;
      include?: any;
      orderBy?: any;
      select?: any;
    },
  ): Promise<PaginatedResult<T>> {
    const skip = (page - 1) * perPage;

    const [total, data] = await Promise.all([
      (this.prismaService[model] as any).count({ where }),
      (this.prismaService[model] as any).findMany({
        take: perPage,
        skip,
        where,
        ...(select ? { select } : {}),
        ...(include ? { include } : {}),
        orderBy,
      }),
    ]);

    const lastPage = Math.ceil(total / perPage);

    return {
      data,
      meta: {
        total,
        lastPage,
        currentPage: page,
        perPage,
        prev: page > 1 ? page - 1 : null,
        next: page < lastPage ? page + 1 : null,
      },
    };
  }
}
