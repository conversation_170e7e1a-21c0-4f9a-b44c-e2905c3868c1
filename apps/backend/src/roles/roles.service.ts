import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateRoleDto } from './dto/create-roles.dto';
import { UpdateRoleDto } from './dto/update-roles.dto';
import { Role } from '../../prisma/app/generated/prisma/client';
import { PaginateQuery } from 'src/pagination/pagination.query';
import { PaginationService } from 'src/pagination/pagination.service';
import { ROLE_NAMES } from './enum/roles.enum';

@Injectable()
export class RolesService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly paginationService: PaginationService,
  ) {}

  async create(createRoleDto: CreateRoleDto) {
    try {
      const role = await this.prismaService.role.create({
        data: createRoleDto,
      });

      return {
        message: 'Role created successfully',
        data: role,
      };
    } catch (error) {
      if (error.code === 'P2002') {
        throw new BadRequestException('Role with this name already exists');
      }
      throw new BadRequestException(error.message);
    }
  }

  async rolesSeeder() {
    const roles = await this.prismaService.role.findMany();
    for (const role of Object.values(ROLE_NAMES)) {
      const existingRole = await this.prismaService.role.findFirst({
        where: {
          name: role,
        },
      });
      if (!existingRole) {
        await this.create({ name: role });
      }
    }
    return {
      message: 'Roles seeded successfully',
      statusCode: 200,
    };
  }

  async findAll(query: PaginateQuery) {
    const { page, perPage } = query;

    return this.paginationService.paginate<Role>('role', {
      page,
      perPage,
      where: {
        deletedAt: null,
        name: {
          not: 'super_admin',
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
  }

  async findByName(name: string) {
    const role = await this.prismaService.role.findFirst({
      where: {
        name,
      },
    });

    if (!role) {
      throw new BadRequestException(`Role with name ${name} not found`);
    }

    return role;
  }

  async findOne(id: number) {
    const role = await this.prismaService.role.findFirst({
      where: {
        id,
        deletedAt: null,
      },
    });

    if (!role) {
      throw new BadRequestException(`Role with ID ${id} not found`);
    }

    return role;
  }

  async update(id: number, updateRoleDto: UpdateRoleDto) {
    try {
      await this.findOne(id);

      const updatedRole = await this.prismaService.role.update({
        where: { id },
        data: updateRoleDto,
      });

      return {
        message: 'Role updated successfully',
        data: updatedRole,
      };
    } catch (error) {
      if (error.code === 'P2002') {
        throw new BadRequestException('Role with this name already exists');
      }
      throw error;
    }
  }

  async remove(id: number) {
    await this.findOne(id);

    await this.prismaService.role.update({
      where: {
        id,
        deletedAt: null,
      },
      data: {
        deletedAt: new Date(),
      },
    });

    return {
      message: 'Role deleted successfully',
      statusCode: 200,
    };
  }
}
