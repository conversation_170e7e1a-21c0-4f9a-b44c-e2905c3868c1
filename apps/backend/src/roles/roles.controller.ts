import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { PaginateQuery } from 'src/pagination/pagination.query';
import { CreateRoleDto } from './dto/create-roles.dto';
import { UpdateRoleDto } from './dto/update-roles.dto';
import { Public } from 'src/auth/decorators/public.decorator';
import { ROLE_NAMES } from './enum/roles.enum';
import { Roles } from './decorators/roles.decorator';

@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Post()
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.create(createRoleDto);
  }

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Get()
  findAll(@Query() query: PaginateQuery) {
    return this.rolesService.findAll(query);
  }

  @Public()
  @Post('seeder')
  seeder() {
    return this.rolesService.rolesSeeder();
  }

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.rolesService.findOne(+id);
  }

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.rolesService.update(+id, updateRoleDto);
  }

  @Roles(ROLE_NAMES.SUPER_ADMIN)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.rolesService.remove(+id);
  }
}
