import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { CreateComponentDto } from './dto/create-component.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { ComponentQuery } from './query/component.query';
import {
  Prisma,
  SelectionType,
} from '../../prisma/app/generated/prisma/client';
@Injectable()
export class ComponentsService {
  constructor(private prismaService: PrismaService) {}

  async create(createComponentDto: CreateComponentDto) {
    // Check if blueprint file exists
    const blueprintFile = await this.prismaService.blueprintFiles.findUnique({
      where: { id: createComponentDto.blueprintFileId },
      include: { blueprintImages: true },
    });

    if (!blueprintFile) {
      throw new NotFoundException(
        `Blueprint file with ID ${createComponentDto.blueprintFileId} not found`,
      );
    }

    if (createComponentDto.selectionType === 'allPages') {
      // Create component for all pages
      const component = await this.prismaService.component.create({
        data: {
          name: createComponentDto.name,
          blueprintFileId: createComponentDto.blueprintFileId,
          geometryType: createComponentDto.geometryType,
          description: createComponentDto.description,
          color: createComponentDto.color,
          shade: createComponentDto.shade,
          selectionType: SelectionType.allPages,
          geometricData: createComponentDto.geometricData,
          blueprintImages: {
            connect: blueprintFile.blueprintImages.map((image) => ({
              id: image.id,
            })),
          },
        },
        include: {
          blueprintImages: true,
        },
      });

      return {
        message: 'Component created successfully for all pages',
        data: component,
      };
    } else {
      // Create component for specific page
      const blueprintImage = await this.prismaService.blueprintImage.findUnique(
        {
          where: { id: createComponentDto.blueprintImageId },
        },
      );

      if (!blueprintImage) {
        throw new NotFoundException(
          `Blueprint image with ID ${createComponentDto.blueprintImageId} not found`,
        );
      }

      const component = await this.prismaService.component.create({
        data: {
          name: createComponentDto.name,
          blueprintFileId: createComponentDto.blueprintFileId,
          geometryType: createComponentDto.geometryType,
          description: createComponentDto.description,
          color: createComponentDto.color,
          shade: createComponentDto.shade,
          selectionType: SelectionType.currentPage,
          geometricData: createComponentDto.geometricData,
          blueprintImages: {
            connect: [{ id: createComponentDto.blueprintImageId }],
          },
        },
        include: {
          blueprintImages: true,
        },
      });

      return {
        message: 'Component created successfully',
        data: component,
      };
    }
  }

  async findAll(query: ComponentQuery, authUserId: string) {
    const where: Prisma.ComponentWhereInput = {
      deletedAt: null,
      blueprintFile: {
        takeoff: {
          userId: authUserId,
        },
      },
    };

    if (query.blueprintFileId) {
      where.blueprintFileId = query.blueprintFileId;
    }

    if (query.blueprintImageId) {
      where.blueprintImages = {
        some: {
          id: query.blueprintImageId,
        },
      };
    }

    if (query.geometryType) {
      where.geometryType = query.geometryType;
    }

    if (query.search) {
      where.name = {
        contains: query.search,
      };
    }

    return this.prismaService.component.findMany({
      where,
      include: {
        blueprintFile: {
          select: {
            id: true,
            fileName: true,
            takeoff: {
              select: {
                id: true,
                name: true,
                userId: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: number, authUserId: string) {
    const component = await this.prismaService.component.findUnique({
      where: { id },
      include: {
        blueprintFile: {
          select: {
            id: true,
            fileName: true,
            takeoff: {
              select: {
                id: true,
                name: true,
                userId: true,
              },
            },
          },
        },
        blueprintImages: {
          select: {
            id: true,
            filename: true,
          },
        },
      },
    });

    if (!component || component.deletedAt) {
      throw new NotFoundException(`Component with ID ${id} not found`);
    }

    if (component.blueprintFile.takeoff.userId !== authUserId) {
      throw new ForbiddenException(
        'You are not authorized to access this component',
      );
    }

    return component;
  }

  async update(
    id: number,
    updateComponentDto: Partial<CreateComponentDto>,
    authUserId: string,
  ) {
    await this.findOne(id, authUserId);

    const component = await this.prismaService.component.update({
      where: { id },
      data: {
        name: updateComponentDto.name,
        geometryType: updateComponentDto.geometryType,
        description: updateComponentDto.description,
        color: updateComponentDto.color,
        shade: updateComponentDto.shade,
        geometricData: updateComponentDto.geometricData,
      },
    });

    return {
      message: 'Component updated successfully',
      data: component,
    };
  }

  async softDelete(id: number, authUserId: string) {
    await this.findOne(id, authUserId);

    await this.prismaService.$transaction(async (tx) => {
      const deletedAt = new Date(); // delete timestamp

      await tx.drawing.updateMany({
        where: { componentId: id },
        data: {
          deletedAt,
        },
      });

      await tx.component.update({
        where: { id },
        data: { deletedAt },
      });
    });

    return {
      message: 'Component deleted successfully',
      data: null,
    };
  }

  async hardDelete(id: number, authUserId: string) {
    await this.findOne(id, authUserId);

    await this.prismaService.$transaction(async (tx) => {
      await tx.drawing.deleteMany({
        where: { componentId: id },
      });

      await tx.component.delete({
        where: { id },
      });
    });

    return {
      message: 'Component deleted successfully',
      data: null,
    };
  }
}
