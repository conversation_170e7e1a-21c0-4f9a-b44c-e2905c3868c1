import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { ComponentsService } from './components.service';
import { CreateComponentDto } from './dto/create-component.dto';
import { ComponentQuery } from './query/component.query';
@Controller('components')
export class ComponentsController {
  constructor(private readonly componentsService: ComponentsService) {}

  @Post()
  create(@Body() createComponentDto: CreateComponentDto) {
    return this.componentsService.create(createComponentDto);
  }

  @Get()
  findAll(@Query() query: ComponentQuery, @Req() { user }) {
    return this.componentsService.findAll(query, user.id);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Req() { user }) {
    return this.componentsService.findOne(+id, user.id);
  }

  @Put(':id')
  update(
    @Param('id') id: string,
    @Body() updateComponentDto: Partial<CreateComponentDto>,
    @Req() { user },
  ) {
    return this.componentsService.update(+id, updateComponentDto, user.id);
  }

  @Delete(':id')
  softDelete(@Param('id') id: string, @Req() { user }) {
    return this.componentsService.softDelete(+id, user.id);
  }

  @Delete(':id/hard')
  hardDelete(@Param('id') id: string, @Req() { user }) {
    return this.componentsService.hardDelete(+id, user.id);
  }
}
