import {
  Is<PERSON>num,
  IsIn,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { GeometryType } from 'prisma/app/generated/prisma/client';

export class CreateComponentDto {
  @IsNotEmpty()
  @IsIn(['allPages', 'currentPage'])
  selectionType: 'allPages' | 'currentPage';

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  blueprintFileId: string;

  @ValidateIf((object) => object.selectionType == 'currentPage')
  @IsNotEmpty()
  @IsString()
  blueprintImageId: string;

  @IsNotEmpty()
  @IsEnum(GeometryType)
  geometryType: GeometryType;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  color?: string;

  @IsOptional()
  @IsString()
  shade?: string;

  @IsOptional()
  geometricData?: any;
}
