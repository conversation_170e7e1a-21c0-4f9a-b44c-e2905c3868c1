import { IsEnum, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';
import { GeometryType } from 'prisma/app/generated/prisma/client';
import { PaginateQuery } from 'src/pagination/pagination.query';

export class ComponentQuery extends PaginateQuery {
  @IsNotEmpty()
  @IsString()
  blueprintFileId?: string;

  @IsOptional()
  @IsString()
  blueprintImageId?: string;

  @IsOptional()
  @IsEnum(GeometryType)
  geometryType?: GeometryType;
}
