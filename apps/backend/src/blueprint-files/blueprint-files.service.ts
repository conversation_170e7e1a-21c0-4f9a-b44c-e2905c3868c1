import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import {
  BlueprintFiles,
  Takeoff,
  BlueprintImage,
} from '../../prisma/app/generated/prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { BlueprintFileDto } from 'src/takeoff/dto/blue-print-file.dto';
import { PaginationService } from 'src/pagination/pagination.service';
import { PaginateQuery } from 'src/pagination/pagination.query';
import { FileUploadService } from 'src/file-upload/file-upload.service';
import { pdfToImg } from 'pdftoimg-js';
import { extname } from 'path';
import { BlueprintImageUpdateDto } from './dto/blueprint-image-update.dto';
import * as pdfParse from 'pdf-parse';

@Injectable()
export class BlueprintFilesService {
  constructor(
    private prismaService: PrismaService,
    private paginationService: PaginationService,
    private fileUploadService: FileUploadService,
    @InjectQueue('pdf-processing') private pdfQueue: Queue,
  ) {}

  async create(takeoffId: number, files: Array<Express.Multer.File>) {
    const takeoff = await this.prismaService.takeoff.findUnique({
      where: { id: takeoffId },
    });

    if (!takeoff) {
      throw new NotFoundException(`Takeoff with ID ${takeoffId} not found`);
    }

    const startTime = Date.now();
    let index = 0;
    for (const file of files) {
      const uploadResponse = await this.fileUploadService.uploadPublicFile(
        file.buffer,
        file.originalname,
        takeoff.userId,
      );
      const { key: awsKey } = uploadResponse;
      const iterationStartTime = Date.now();
      console.log(`Processing blueprint file ${index + 1} of ${files.length}`);
      const blueprintFiles = await this.prismaService.blueprintFiles.create({
        data: {
          fileName: file.originalname,
          fileUrl: uploadResponse.file_url,
          takeoffId,
          awsKey: awsKey,
        },
      });
      try {
        await this.convertPdfToImages(blueprintFiles.id, file.buffer, awsKey);
      } catch (error) {
        console.error(`Error processing blueprint file: ${error.message}`);
        throw new Error(`Failed to process blueprint file: ${error.message}`);
      }

      const iterationEndTime = Date.now();
      console.log(
        `Blueprint file processing took ${iterationEndTime - iterationStartTime}ms`,
      );
      index++;
    }
    const totalTime = Date.now() - startTime;
    console.log(`Total processing time: ${totalTime}ms`);

    return {
      message: 'Blueprint files created successfully',
    };
  }

  async findAllBluePrintImagesByBlueprintFileId(
    blueprintFileId: string,
    query: PaginateQuery,
  ) {
    await this.findOne(blueprintFileId);
    return this.paginationService.paginate<BlueprintImage>('blueprintImage', {
      page: query.page,
      perPage: query.perPage,
      where: { blueprintFileId },
      orderBy: {
        pageNumber: 'asc',
      },
    });
  }

  async findBluePrintImageById(blueprintImageId: string, authUserId: string) {
    const blueprintImage = await this.prismaService.blueprintImage.findUnique({
      where: { id: blueprintImageId },
      include: {
        blueprintFile: {
          include: {
            takeoff: true,
          },
        },
      },
    });
    if (!blueprintImage) {
      throw new NotFoundException(
        `Blueprint image with ID ${blueprintImageId} not found`,
      );
    }
    if (blueprintImage.blueprintFile.takeoff.userId !== authUserId) {
      throw new ForbiddenException(
        'You are not allowed to access this resource',
      );
    }
    return blueprintImage;
  }

  async updateBlueprintImage(
    blueprintImageId: string,
    authUserId: string,
    blueprintImageUpdateDto: BlueprintImageUpdateDto,
  ) {
    await this.findBluePrintImageById(blueprintImageId, authUserId);
    const { scale, dimensions } = blueprintImageUpdateDto;
    const blueprintImage = await this.prismaService.blueprintImage.update({
      where: {
        id: blueprintImageId,
      },
      data: { scale, dimensions },
    });
    return {
      message: 'Blueprint image updated successfully',
      data: blueprintImage,
    };
  }

  async findByTakeoffId(
    takeoffId: number,
    query: PaginateQuery,
    authUserId: string,
  ) {
    const takeoff = await this.prismaService.takeoff.findUnique({
      where: { id: takeoffId },
    });

    if (!takeoff) {
      throw new NotFoundException(`Takeoff with ID ${takeoffId} not found`);
    }
    if (takeoff.userId !== authUserId) {
      throw new ForbiddenException(
        'You are not allowed to access this resource',
      );
    }
    const { page, perPage } = query;

    return this.paginationService.paginate<BlueprintFiles>('blueprintFiles', {
      page,
      perPage,
      where: {
        takeoffId,
        deletedAt: null,
      },
      include: {
        takeoff: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string): Promise<BlueprintFiles & { takeoff: Takeoff }> {
    const blueprintFile = await this.prismaService.blueprintFiles.findUnique({
      where: { id },
      include: {
        takeoff: true,
      },
    });
    if (!blueprintFile || blueprintFile.deletedAt) {
      throw new NotFoundException(`Blueprint file with ID ${id} not found`);
    }

    return blueprintFile;
  }

  async update(id: string, dto: Partial<BlueprintFileDto>) {
    await this.findOne(id);
    const blueprintFile = await this.prismaService.blueprintFiles.update({
      where: { id },
      data: dto,
    });

    return {
      message: 'Blueprint file updated successfully',
      data: blueprintFile,
    };
  }

  async softDelete(id: string) {
    await this.findOne(id);
    const blueprintFile = await this.prismaService.blueprintFiles.update({
      where: { id },
      data: { deletedAt: new Date() },
    });

    return {
      message: 'Blueprint file deleted successfully',
      data: blueprintFile,
    };
  }

  async hardDelete(id: string) {
    await this.findOne(id);
    await this.prismaService.blueprintFiles.delete({
      where: { id },
    });

    return {
      message: 'Blueprint file deleted successfully',
      data: null,
    };
  }

  async convertPdfToImages(
    blueprintFileId: string,
    pdfBuffer: Buffer,
    pdfAwsKey: string,
  ) {
    const blueprintFile = await this.findOne(blueprintFileId);
    const uploaderId = blueprintFile.takeoff.userId;

    console.log('Converting first batch of pages...');
    const BATCH_SIZE = 10; // Number of pages to process in each batch

    // Get total number of pages
    const totalPages = (await pdfParse(pdfBuffer)).numpages;
    console.log(`Total pages in PDF: ${totalPages}`);

    // Process first batch immediately
    const firstBatchSize = Math.min(BATCH_SIZE, totalPages);
    const firstBatchStartTime = Date.now();
    console.log(`Processing first ${firstBatchSize} pages...`);
    const firstBatchImages = await pdfToImg(pdfBuffer, {
      pages: { startPage: 1, endPage: firstBatchSize },
      imgType: 'png',
      scale: 1.5,
    });
    const firstBatchEndTime = Date.now();
    console.log(
      `First batch processing took ${firstBatchEndTime - firstBatchStartTime}ms`,
    );
    // Upload first batch
    const uploadPromises = firstBatchImages.map(async (base64Image, index) => {
      const cleanBase64Image = this.cleanBase64(base64Image);
      const filename = `${this.getFileName(blueprintFile.fileName)}_page_${index + 1}.png`;
      const imageBuffer = Buffer.from(cleanBase64Image, 'base64');

      return {
        uploadResponse: await this.fileUploadService.uploadPublicFile(
          imageBuffer,
          filename,
          uploaderId,
        ),
        pageNumber: index + 1,
      };
    });
    const uploadStartTime = Date.now();
    console.log(`Uploading first ${firstBatchSize} images...`);
    const uploadedImages = await Promise.all(uploadPromises);
    console.log(`First batch upload took ${Date.now() - uploadStartTime}ms`);
    const blueprintImages = uploadedImages.map((result) => ({
      blueprintFileId,
      filename: result.uploadResponse['file_name'],
      path: result.uploadResponse['file_url'],
      awsKey: result.uploadResponse['key'],
      pageNumber: result['pageNumber'],
      scale: {
        num_metric: 1,
        num_unit: 'inch',
        den_metric: 8,
        den_unit: 'ft',
      },
      dimensions: {
        width: 36,
        height: 24,
        unit: 'inch',
      },
    }));

    await this.prismaService.blueprintImage.createMany({
      data: blueprintImages,
    });

    // Queue remaining batches for background processing
    if (totalPages > BATCH_SIZE) {
      console.log('Queueing remaining batches for background processing...');
      const queuePromises = [];
      for (
        let startPage = BATCH_SIZE;
        startPage < totalPages;
        startPage += BATCH_SIZE
      ) {
        const endPage = Math.min(startPage + BATCH_SIZE, totalPages);
        const jobOptions = {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: false,
          removeOnFail: false,
        };

        const queuePromise = this.pdfQueue.add(
          'convert-pdf-batch',
          {
            blueprintFileId,
            awsKey: pdfAwsKey,
            startPage: startPage + 1,
            endPage,
            uploaderId,
            fileName: blueprintFile.fileName,
          },
          jobOptions,
        );

        queuePromises.push(queuePromise);
      }

      try {
        await Promise.all(queuePromises);
        console.log('All remaining batches queued successfully');
      } catch (error) {
        console.error('Error queueing batch jobs:', error);
        throw new Error(
          `Failed to queue background processing jobs: ${error.message}`,
        );
      }
    }

    return {
      message: `First ${firstBatchSize} pages converted successfully. ${
        totalPages > BATCH_SIZE
          ? `Remaining ${totalPages - firstBatchSize} pages will be processed in the background.`
          : ''
      }`,
      data: {
        processed: firstBatchSize,
        total: totalPages,
        remaining: Math.max(0, totalPages - firstBatchSize),
      },
    };
  }

  private getFileName(fileName: string) {
    const extension = extname(fileName);
    return fileName.replace(extension, '');
  }

  private cleanBase64(base64: string): string {
    base64 = base64.replace(/^data:.*;base64,/, '');
    base64 = base64.replace(/\s/g, '');
    return base64;
  }
}
