import { Process, Processor } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { Job } from 'bull';
import { PrismaService } from '../prisma/prisma.service';
import { FileUploadService } from '../file-upload/file-upload.service';
import { pdfToImg } from 'pdftoimg-js';
import { GetObjectCommand } from '@aws-sdk/client-s3';

interface PdfProcessJob {
  blueprintFileId: string;
  awsKey: string;
  startPage: number;
  endPage: number;
  uploaderId: string;
  fileName: string;
}

@Injectable()
@Processor('pdf-processing')
export class PdfProcessorService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly fileUploadService: FileUploadService,
  ) {}

  @Process('convert-pdf-batch')
  async processPdfBatch(job: Job<PdfProcessJob>) {
    const {
      blueprintFileId,
      awsKey,
      startPage,
      endPage,
      uploaderId,
      fileName,
    } = job.data;

    const checkIfPagesExist = await this.checkIfPagesExist(
      blueprintFileId,
      startPage,
      endPage,
    );

    // Skip if all pages already processed
    if (checkIfPagesExist) {
      console.log(
        `Pages ${startPage} to ${endPage} already processed, skipping`,
      );
      await job.progress(100);
      return {
        success: true,
        message: `Pages ${startPage} to ${endPage} already processed`,
      };
    }

    console.log(`Processing PDF batch pages ${startPage} to ${endPage}`);
    await job.progress(0);

    try {
      // Get the file from S3
      const s3Client = this.fileUploadService['getS3Client']();
      const getObjectCommand = new GetObjectCommand({
        Bucket: this.fileUploadService['getPublicBucketName'](),
        Key: awsKey,
      });

      const response = await s3Client.send(getObjectCommand);
      const pdfBuffer = await response.Body.transformToByteArray();

      await job.progress(20);
      console.log('PDF file fetched from S3, starting image conversion...');

      const images = await pdfToImg(Buffer.from(pdfBuffer), {
        pages: { startPage: startPage, endPage: endPage },
        imgType: 'png',
        scale: 1.5,
      });

      await job.progress(50);
      console.log(`PDF pages converted to images, starting upload...`);

      const uploadPromises = images.map(async (base64Image, index) => {
        const actualPageNumber = startPage + index;
        const cleanBase64Image = this.cleanBase64(base64Image);
        const filename = `${this.getFileName(fileName)}_page_${actualPageNumber}.png`;
        const imageBuffer = Buffer.from(cleanBase64Image, 'base64');

        return {
          uploadResponse: await this.fileUploadService.uploadPublicFile(
            imageBuffer,
            filename,
            uploaderId,
          ),
          pageNumber: actualPageNumber,
        };
      });

      const uploadedImages = await Promise.all(uploadPromises);
      await job.progress(75);
      console.log('Images uploaded to S3, creating database records...');

      const blueprintImages = uploadedImages.map((result) => ({
        blueprintFileId,
        filename: result.uploadResponse['file_name'],
        path: result.uploadResponse['file_url'],
        awsKey: result.uploadResponse['key'],
        pageNumber: result.pageNumber,
        scale: {
          num_metric: 1,
          num_unit: 'inch',
          den_metric: 8,
          den_unit: 'ft',
        },
        dimensions: {
          width: 36,
          height: 24,
          unit: 'inch',
        },
      }));

      await this.prismaService.blueprintImage.createMany({
        data: blueprintImages,
      });

      console.log(
        `Successfully processed batch pages ${startPage} to ${endPage}`,
      );
      await job.progress(100);
      return {
        success: true,
        message: `Processed pages ${startPage} to ${endPage}`,
      };
    } catch (error) {
      console.error(`Error processing PDF batch: ${error.message}`);
      console.error(error.stack);
      await job.moveToFailed({ message: error.message }, true);
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }

  private getFileName(fileName: string) {
    const extension = fileName.split('.').pop();
    return fileName.replace(`.${extension}`, '');
  }

  private cleanBase64(base64: string): string {
    base64 = base64.replace(/^data:.*;base64,/, '');
    base64 = base64.replace(/\s/g, '');
    return base64;
  }

  private async checkIfPagesExist(
    blueprintFileId: string,
    startPage: number,
    endPage: number,
  ): Promise<boolean> {
    const existingImages = await this.prismaService.blueprintImage.findMany({
      where: {
        blueprintFileId,
        pageNumber: {
          gte: startPage,
          lte: endPage,
        },
      },
    });
    return existingImages.length === endPage - startPage + 1;
  }
}
