import {
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Body,
  Query,
  UploadedFiles,
  HttpStatus,
  ParseFilePipeBuilder,
  UseInterceptors,
  Req,
  Patch,
} from '@nestjs/common';
import { BlueprintFilesService } from './blueprint-files.service';
import { PaginateQuery } from 'src/pagination/pagination.query';
import { FilesInterceptor } from '@nestjs/platform-express';
import { BlueprintImageUpdateDto } from './dto/blueprint-image-update.dto';

@Controller('blueprint-files')
export class BlueprintFilesController {
  constructor(private readonly blueprintFilesService: BlueprintFilesService) {}
  @Post('takeoff/:takeoffId')
  @UseInterceptors(FilesInterceptor('files', 10))
  async addBlueprintFile(
    @Param('takeoffId') takeoffId: string,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addMaxSizeValidator({
          maxSize: 1024 * 1024 * 50,
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    files: Array<Express.Multer.File>,
  ) {
    return this.blueprintFilesService.create(+takeoffId, files);
  }

  @Get('takeoff/:takeoffId')
  async findBlueprintFiles(
    @Param('takeoffId') takeoffId: string,
    @Query() query: PaginateQuery,
    @Req() { user },
  ) {
    return this.blueprintFilesService.findByTakeoffId(
      +takeoffId,
      query,
      user.id,
    );
  }

  @Delete(':id')
  async removeBlueprintFile(@Param('id') id: string) {
    return this.blueprintFilesService.softDelete(id);
  }

  @Get(':blueprintFileId/images')
  async findBlueprintImages(
    @Param('blueprintFileId') blueprintFileId: string,
    @Query() query: PaginateQuery,
  ) {
    return this.blueprintFilesService.findAllBluePrintImagesByBlueprintFileId(
      blueprintFileId,
      query,
    );
  }

  @Get('/images/:blueprintImageId')
  async findBlueprintImageById(
    @Param('blueprintImageId') blueprintImageId: string,
    @Req() { user },
  ) {
    return this.blueprintFilesService.findBluePrintImageById(
      blueprintImageId,
      user.id,
    );
  }

  @Patch('/images/:blueprintImageId')
  async updateBlueprintImage(
    @Param('blueprintImageId') blueprintImageId: string,
    @Req() { user },
    @Body() blueprintImageUpdateDto: BlueprintImageUpdateDto,
  ) {
    return this.blueprintFilesService.updateBlueprintImage(
      blueprintImageId,
      user.id,
      blueprintImageUpdateDto,
    );
  }
}
