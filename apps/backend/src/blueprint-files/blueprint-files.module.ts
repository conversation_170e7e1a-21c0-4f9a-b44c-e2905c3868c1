import { Module } from '@nestjs/common';
import { BlueprintFilesService } from './blueprint-files.service';
import { BlueprintFilesController } from './blueprint-files.controller';
import { PrismaModule } from 'src/prisma/prisma.module';
import { PaginationModule } from 'src/pagination/pagination.module';
import { FileUploadModule } from 'src/file-upload/file-upload.module';
import { BullModule } from '@nestjs/bull';
import { PdfProcessorService } from './pdf-processor.service';

@Module({
  imports: [
    PrismaModule,
    PaginationModule,
    FileUploadModule,
    BullModule.registerQueue({
      name: 'pdf-processing',
    }),
  ],
  controllers: [BlueprintFilesController],
  providers: [BlueprintFilesService, PdfProcessorService],
  exports: [BlueprintFilesService],
})
export class BlueprintFilesModule {}
