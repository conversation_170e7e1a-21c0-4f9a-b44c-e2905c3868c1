import { Injectable } from '@nestjs/common';
import { ENGAGESPOT_WORKFLOW } from './enagagespot.workflows';
import { ENGAGESPOT_WORKFLOWS } from './enagagespot.workflows';
import { EngagespotClient } from '@engagespot/node';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EngagespotService {
  constructor(private readonly configService: ConfigService) {}

  private getEngagespotApiKey() {
    return this.configService.get<string>(
      'ENGAGESPOT_API_KEY',
      '5bytiwk34dasz56wgesqlh',
    );
  }

  private getEngagespotApiSecret() {
    return this.configService.get<string>(
      'ENGAGESPOT_API_SECRET',
      'ao1etctbqdlralfjd09hir7dh27f4eh2c988g34hdbfi0g82',
    );
  }

  async send(
    engagespotWorkflow: ENGAGESPOT_WORKFLOW,
    recipients: Array<string>,
    data?: object,
    sendAt?: string,
  ) {
    const workflowIdentifier = ENGAGESPOT_WORKFLOWS[engagespotWorkflow];
    try {
      const client = await this.engageSpotClient();
      client.send({
        notification: {
          workflow: { identifier: workflowIdentifier },
          data,
        },
        sendTo: {
          recipients,
        },
        sendAt,
      });

      return true;
    } catch (err) {
      console.error(err);
      return false;
    }
  }

  async createOrUpdateUser(
    identifier: string,
    options: {
      name?: string;
      email?: string;
      phoneNumber?: string;
    },
  ) {
    try {
      const client = await this.engageSpotClient();
      const { name, phoneNumber, email } = options;

      await client.createOrUpdateUser(identifier, {
        phoneNumber,
        name,
        email,
      });
      return true;
    } catch (err) {
      return false;
    }
  }

  async engageSpotClient() {
    return EngagespotClient({
      apiKey: this.getEngagespotApiKey(),
      apiSecret: this.getEngagespotApiSecret(),
    });
  }
}
