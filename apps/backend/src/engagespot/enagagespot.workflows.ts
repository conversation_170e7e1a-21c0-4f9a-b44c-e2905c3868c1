import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';

const configService = new ConfigService();
config();

export enum ENGAGESPOT_WORKFLOW {
  FORGOT_PASSWORD = 'forgot_password',
  EMAIL_VERIFICATION = 'email_verification',
}

export const ENGAGESPOT_WORKFLOWS: Record<ENGAGESPOT_WORKFLOW, string> = {
  [ENGAGESPOT_WORKFLOW.FORGOT_PASSWORD]: configService.get<string>(
    'FORGOT_PASSWORD_WORKFLOW_ID',
    'forgot_password',
  ),
  [ENGAGESPOT_WORKFLOW.EMAIL_VERIFICATION]: configService.get<string>(
    'EMAIL_VERIFICATION_WORKFLOW_ID',
    'email_verification',
  ),
};
