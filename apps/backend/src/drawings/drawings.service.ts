import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateDrawingDto } from './dto/create-drawing.dto';
import { UpdateDrawingDto } from './dto/update-drawing.dto';
import { DrawingQuery } from './dto/drawing.query';
import { Prisma } from '../../prisma/app/generated/prisma/client';

@Injectable()
export class DrawingsService {
  constructor(private prismaService: PrismaService) {}

  async create(createDrawingDto: CreateDrawingDto, authUserId: string) {
    // Verify blueprint image exists and user has access
    const blueprintImage = await this.prismaService.blueprintImage.findUnique({
      where: { id: createDrawingDto.blueprintImageId },
      include: {
        blueprintFile: {
          include: {
            takeoff: true,
          },
        },
      },
    });

    if (!blueprintImage) {
      throw new NotFoundException(
        `Blueprint image with ID ${createDrawingDto.blueprintImageId} not found`,
      );
    }

    if (blueprintImage.blueprintFile.takeoff.userId !== authUserId) {
      throw new ForbiddenException(
        'You are not authorized to create drawings for this blueprint',
      );
    }

    // If componentId is provided, verify it exists and belongs to the same blueprint file
    if (createDrawingDto.componentId) {
      const component = await this.prismaService.component.findUnique({
        where: {
          id: createDrawingDto.componentId,
          blueprintFileId: blueprintImage.blueprintFileId,
          deletedAt: null,
        },
      });

      if (!component) {
        throw new NotFoundException(
          `Component with ID ${createDrawingDto.componentId} not found`,
        );
      }

      if (component.blueprintFileId !== blueprintImage.blueprintFileId) {
        throw new ForbiddenException(
          'Component does not belong to the same blueprint file',
        );
      }
    }

    const drawing = await this.prismaService.drawing.create({
      data: {
        blueprintImageId: createDrawingDto.blueprintImageId,
        componentId: createDrawingDto.componentId,
        config: createDrawingDto.config,
      },
      include: {
        blueprintImage: true,
        component: true,
      },
    });

    return {
      message: 'Drawing created successfully',
      data: drawing,
    };
  }

  async findAll(query: DrawingQuery, authUserId: string) {
    const where: Prisma.DrawingWhereInput = {
      deletedAt: null,
      blueprintImage: {
        blueprintFile: {
          takeoff: {
            userId: authUserId,
          },
        },
      },
    };

    if (query.blueprintImageId) {
      where.blueprintImageId = query.blueprintImageId;
    }

    if (query.componentId) {
      where.componentId = query.componentId;
    }

    return this.prismaService.drawing.findMany({
      where,
      include: {
        blueprintImage: {
          select: {
            id: true,
            filename: true,
            blueprintFile: {
              select: {
                id: true,
                fileName: true,
                takeoff: {
                  select: {
                    id: true,
                    userId: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
        component: {
          select: {
            id: true,
            name: true,
            color: true,
            shade: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: number, authUserId: string) {
    const drawing = await this.prismaService.drawing.findUnique({
      where: { id },
      include: {
        blueprintImage: {
          include: {
            blueprintFile: {
              include: {
                takeoff: true,
              },
            },
          },
        },
        component: true,
      },
    });

    if (!drawing || drawing.deletedAt) {
      throw new NotFoundException(`Drawing with ID ${id} not found`);
    }

    if (drawing.blueprintImage.blueprintFile.takeoff.userId !== authUserId) {
      throw new ForbiddenException(
        'You are not authorized to access this drawing',
      );
    }

    return drawing;
  }

  async update(
    id: number,
    updateDrawingDto: UpdateDrawingDto,
    authUserId: string,
  ) {
    await this.findOne(id, authUserId);
    if (updateDrawingDto.componentId) {
      const component = await this.prismaService.component.findUnique({
        where: {
          id: updateDrawingDto.componentId,
          blueprintImages: {
            some: {
              id: updateDrawingDto.blueprintImageId,
            },
          },
          deletedAt: null,
        },
      });

      if (!component) {
        throw new NotFoundException(
          `Component with ID ${updateDrawingDto.componentId} not found`,
        );
      }
    }
    const drawing = await this.prismaService.drawing.update({
      where: { id },
      data: {
        componentId: updateDrawingDto.componentId,
        config: updateDrawingDto.config,
      },
      include: {
        blueprintImage: true,
        component: true,
      },
    });

    return {
      message: 'Drawing updated successfully',
      data: drawing,
    };
  }

  async softDelete(id: number, authUserId: string) {
    await this.findOne(id, authUserId);

    const drawing = await this.prismaService.drawing.update({
      where: { id },
      data: { deletedAt: new Date() },
    });

    return {
      message: 'Drawing deleted successfully',
      data: drawing,
    };
  }

  async hardDelete(id: number, authUserId: string) {
    await this.findOne(id, authUserId);

    await this.prismaService.drawing.delete({
      where: { id },
    });

    return {
      message: 'Drawing deleted successfully',
      data: null,
    };
  }
}
