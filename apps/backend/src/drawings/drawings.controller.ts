import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { DrawingsService } from './drawings.service';
import { CreateDrawingDto } from './dto/create-drawing.dto';
import { UpdateDrawingDto } from './dto/update-drawing.dto';
import { DrawingQuery } from './dto/drawing.query';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';

@Controller('drawings')
@UseGuards(JwtAuthGuard)
export class DrawingsController {
  constructor(private readonly drawingsService: DrawingsService) {}

  @Post()
  create(@Body() createDrawingDto: CreateDrawingDto, @Req() { user }) {
    return this.drawingsService.create(createDrawingDto, user.id);
  }

  @Get()
  findAll(@Query() query: DrawingQuery, @Req() { user }) {
    return this.drawingsService.findAll(query, user.id);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Req() { user }) {
    return this.drawingsService.findOne(+id, user.id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateDrawingDto: UpdateDrawingDto,
    @Req() { user },
  ) {
    return this.drawingsService.update(+id, updateDrawingDto, user.id);
  }

  @Delete(':id')
  softDelete(@Param('id') id: string, @Req() { user }) {
    return this.drawingsService.softDelete(+id, user.id);
  }

  @Delete(':id/hard')
  hardDelete(@Param('id') id: string, @Req() { user }) {
    return this.drawingsService.hardDelete(+id, user.id);
  }
}
