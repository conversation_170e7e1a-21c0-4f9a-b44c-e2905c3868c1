import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from 'src/users/users.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import * as bcrypt from 'bcrypt';
import { ROLE_NAMES } from 'src/roles/enum/roles.enum';
import { NotificationsService } from 'src/notifications/notifications.service';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { UserVerificationService } from 'src/user-verification/user-verification.service';
import {
  VerificationType,
  UserStatus,
} from '../../prisma/app/generated/prisma/client';
import { addHours, addMinutes } from 'date-fns';
import { uuid } from 'uuidv4';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
    private readonly notificationsService: NotificationsService,
    private readonly engagespotService: EngagespotService,
    private readonly userVerificationService: UserVerificationService,
  ) {}

  async register(registerDto: RegisterDto) {
    const user = await this.usersService.create({
      ...registerDto,
      roleName: ROLE_NAMES.USER,
    });
    const { password, refreshToken, ...result } = user;

    const tokens = await this.getTokens(user.id, user.email);
    await this.usersService.updateRefreshToken(user.id, tokens.refreshToken);
    await this.engagespotService.createOrUpdateUser(user.id, {
      name: user.fullName,
      email: user.email,
      phoneNumber: user.phoneNumber,
    });
    await this.sendEmailVerificationOTP(user.id);

    return {
      message: 'User successfully registered',
      userId: user.id,
    };
  }

  async sendEmailVerificationOTP(userId: string) {
    const otp = await this.generateOtp();
    let user = await this.usersService.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const expirationTimeInMin = 15;

    await this.userVerificationService.forceExpireUserVerifications(
      user.id,
      VerificationType.EMAIL,
    );

    await this.userVerificationService.create({
      userId: user.id,
      verificationType: VerificationType.EMAIL,
      code: otp,
      expiresAt: addMinutes(new Date(), expirationTimeInMin), // 15 minutes from now
    });

    await this.notificationsService.sendEmailVerificationNotification(
      user.id,
      otp,
      `${expirationTimeInMin} Min`,
    );

    return {
      message: 'OTP sent successfully',
    };
  }

  private async generateOtp(length: number = 6) {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(min + Math.random() * (max - min + 1)).toString();
  }

  async login(loginDto: LoginDto) {
    // Determine if the user is using email or phone number
    const identifier = loginDto.email;

    if (!identifier) {
      throw new BadRequestException('Email or phone number is required');
    }

    const user = await this.usersService.validateUser(
      identifier,
      loginDto.password,
    );

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (!user.emailVerifiedAt) {
      throw new UnauthorizedException({
        message: 'Email not verified',
        code: 'EMAIL_NOT_VERIFIED',
        email: user.id,
      });
    }

    const tokens = await this.getTokens(user.id, user.email);
    await this.usersService.updateRefreshToken(user.id, tokens.refreshToken);

    const { password, refreshToken, ...result } = user;

    return {
      ...tokens,
      user: result,
    };
  }

  async logout(userId: string) {
    await this.usersService.updateRefreshToken(userId, null);
    return { message: 'Logout successful' };
  }

  async refreshTokens(userId: string, refreshToken: string) {
    const user = await this.usersService.findById(userId);

    if (!user || !user.refreshToken) {
      throw new UnauthorizedException(
        'User not found or refresh token revoked',
      );
    }

    const refreshTokenMatches = await bcrypt.compare(
      refreshToken,
      user.refreshToken,
    );

    if (!refreshTokenMatches) {
      throw new UnauthorizedException(
        'User not found or refresh token revoked',
      );
    }

    const tokens = await this.getTokens(user.id, user.email);
    await this.usersService.updateRefreshToken(user.id, tokens.refreshToken);

    return tokens;
  }

  async validateOtp(userId: string, otp: string) {
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new UnauthorizedException('Invalid phone number');
    }

    //bypassing the otp for development
    const APP_ENVIRONMENT = this.configService.get<string>(
      'APP_ENVIRONMENT',
      'development',
    );

    if (APP_ENVIRONMENT !== 'development' || otp !== '728888') {
      const userVerification = await this.userVerificationService.verify({
        userId: user.id,
        verificationType: VerificationType.EMAIL,
        code: otp,
      });

      if (!userVerification) {
        throw new UnauthorizedException('Invalid OTP');
      }
    }

    if (!user.emailVerifiedAt) {
      await this.usersService.update(user.id, {
        emailVerifiedAt: new Date(),
        status: UserStatus.active,
      });
    }

    const tokens = await this.getTokens(user.id, user.email);
    await this.usersService.updateRefreshToken(user.id, tokens.refreshToken);

    const { password, refreshToken, ...result } =
      await this.usersService.findById(user.id);

    return {
      ...tokens,
      user: result,
    };
  }

  private async getTokens(userId: string, email: string) {
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        {
          sub: userId,
          email,
        },
        {
          secret: this.configService.get<string>('JWT_ACCESS_SECRET'),
          expiresIn: this.configService.get<string>('JWT_ACCESS_EXPIRATION'),
        },
      ),
      this.jwtService.signAsync(
        {
          sub: userId,
          email,
        },
        {
          secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
          expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRATION'),
        },
      ),
    ]);

    return {
      type: 'Bearer',
      accessToken,
      refreshToken,
      accessTokenExpiresIn: this.configService.get<string>(
        'jwt.accessExpiration',
      ),
      refreshTokenExpiresIn: this.configService.get<string>(
        'jwt.refreshExpiration',
      ),
    };
  }
  async forgotPassword(email: string) {
    const user = await this.usersService.findByEmail(email);

    if (!user) {
      throw new BadRequestException('User not found');
    }

    const resetToken = uuid();
    const resetTokenExpiresAt = addHours(new Date(), 1); // 1 hour

    this.usersService.update(user.id, { resetToken, resetTokenExpiresAt });
    this.notificationsService.sendForgotPasswordNotification(
      user.id,
      resetToken,
      '1 Hr',
    );
    //send email with reset token
    // this.notificationsService.forgotPasswordNotification(user, resetToken);

    return {
      message: 'Password reset instructions have been sent to your email',
      statusCode: 200,
    };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { token, password } = resetPasswordDto;

    const user = await this.usersService.findByResetToken(token);

    if (!user) {
      throw new BadRequestException('Invalid reset token');
    }

    if (user.resetTokenExpiresAt < new Date()) {
      throw new BadRequestException('Reset token has expired');
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    await this.usersService.update(user.id, {
      resetToken: null,
      resetTokenExpiresAt: null,
      password: hashedPassword,
    });

    return {
      message: 'Password reset successfully',
      statusCode: 200,
    };
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto) {
    const { current_password, new_password } = changePasswordDto;
    const user = await this.usersService.findById(userId);

    if (!user || !(await bcrypt.compare(current_password, user.password))) {
      throw new BadRequestException('Current password is incorrect');
    }

    const hashedPassword = await bcrypt.hash(new_password, 10);

    await this.usersService.update(user.id, {
      password: hashedPassword,
    });

    return {
      message: 'Password changed successfully',
      statusCode: 200,
    };
  }
}
