import {
  IsEmail,
  IsNotEmpty,
  IsO<PERSON>al,
  IsString,
  IsStrongPassword,
  MaxLength,
} from 'class-validator';

export class RegisterDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  fullName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  organizationName: string;

  @IsString()
  @IsNotEmpty()
  @IsStrongPassword(
    {
      minLength: 8,
      minLowercase: 1,
      minUppercase: 1,
      minNumbers: 1,
      minSymbols: 1,
    },
    {
      message:
        'Password must be at least 8 characters in length and include at least one uppercase letter, one numeric digit, and one special character (e.g., @, $, !, %, *, ?, &).',
    },
  )
  password: string;
}
