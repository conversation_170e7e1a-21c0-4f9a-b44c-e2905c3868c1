import {
  IsNotEmpty,
  IsString,
  IsStrongPassword,
  MaxLength,
} from 'class-validator';

export class ChangePasswordDto {
  @IsString()
  @IsNotEmpty()
  current_password: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  @IsStrongPassword(
    {
      minLength: 8,
      minLowercase: 1,
      minUppercase: 1,
      minNumbers: 1,
      minSymbols: 1,
    },
    {
      message:
        'New-Password must be at least 8 characters in length and include at least one uppercase letter, one numeric digit, and one special character (e.g., @, $, !, %, *, ?, &).',
    },
  )
  readonly new_password: string;
}
