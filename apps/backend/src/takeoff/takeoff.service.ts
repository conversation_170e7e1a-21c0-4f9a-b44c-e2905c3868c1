import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateTakeoffDto } from './dto/create-takeoff.dto';
import {
  Takeoff,
  TakeoffStatus,
} from '../../prisma/app/generated/prisma/client';
import { PaginationService } from 'src/pagination/pagination.service';
import { PaginatedResult } from 'src/pagination/pagination.result';
import { UpdateTakeoffDto } from './dto/update-takeodd.dto';
import { TakeOffQueryDto } from './dto/take-off-query.dto';
import { BlueprintFilesService } from 'src/blueprint-files/blueprint-files.service';
@Injectable()
export class TakeoffService {
  constructor(
    private prismaService: PrismaService,
    private paginationService: PaginationService,
    private blueprintFilesService: BlueprintFilesService,
  ) {}

  async create(
    userId: string,
    dto: CreateTakeoffDto,
    files: Array<Express.Multer.File>,
  ): Promise<{ message: string; data: Takeoff }> {
    const takeoff = await this.prismaService.takeoff.create({
      data: {
        ...dto,
        userId,
      },
      include: {
        blueprintFiles: {
          where: { deletedAt: null },
        },
      },
    });
    await this.blueprintFilesService.create(takeoff.id, files);
    return {
      message: 'Takeoff created successfully',
      data: takeoff,
    };
  }

  async findAll(
    userId: string,
    query: TakeOffQueryDto,
  ): Promise<PaginatedResult<Takeoff>> {
    const { perPage, page, search, orderBySubmissionDate, status } = query;

    return this.paginationService.paginate<Takeoff>('takeoff', {
      page,
      perPage,
      where: {
        userId,
        deletedAt: null,
        ...(search && {
          name: { contains: search },
        }),
        ...(status && {
          status,
        }),
      },
      include: {
        _count: {
          select: {
            blueprintFiles: true,
          },
        },
      },
      orderBy: {
        submissionDate: orderBySubmissionDate,
      },
    });
  }

  async findOne(id: number, userId: string): Promise<Takeoff> {
    const takeoff = await this.prismaService.takeoff.findFirst({
      where: {
        id,
        userId,
        deletedAt: null,
      },
      include: {
        blueprintFiles: {
          where: { deletedAt: null },
        },
      },
    });

    if (!takeoff) {
      throw new NotFoundException(`Takeoff with ID ${id} not found`);
    }

    return takeoff;
  }

  async update(
    id: number,
    userId: string,
    dto: UpdateTakeoffDto,
  ): Promise<{ message: string; data: Takeoff }> {
    await this.findOne(id, userId);
    const takeoff = await this.prismaService.takeoff.update({
      where: { id },
      data: {
        ...dto,
      },
      include: {
        blueprintFiles: {
          where: { deletedAt: null },
        },
      },
    });

    return {
      message: 'Takeoff updated successfully',
      data: takeoff,
    };
  }

  async updateStatus(
    id: number,
    userId: string,
    status: TakeoffStatus,
  ): Promise<{ message: string; data: Takeoff }> {
    await this.findOne(id, userId);

    const takeoff = await this.prismaService.takeoff.update({
      where: { id },
      data: { status },
    });

    return {
      message: 'Takeoff status updated successfully',
      data: takeoff,
    };
  }

  async softDelete(
    id: number,
    userId: string,
  ): Promise<{ message: string; data: Takeoff }> {
    await this.findOne(id, userId);

    const takeoff = await this.prismaService.takeoff.update({
      where: { id },
      data: { deletedAt: new Date() },
    });

    return {
      message: 'Takeoff deleted successfully',
      data: takeoff,
    };
  }

  async hardDelete(
    id: number,
    userId: string,
  ): Promise<{ message: string; data: null }> {
    await this.findOne(id, userId);

    await this.prismaService.blueprintFiles.deleteMany({
      where: { takeoffId: id },
    });

    await this.prismaService.takeoff.delete({
      where: { id },
    });

    return {
      message: 'Takeoff deleted successfully',
      data: null,
    };
  }
}
