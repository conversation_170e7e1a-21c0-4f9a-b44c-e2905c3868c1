import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Delete,
  Query,
  Req,
  Patch,
  ParseFilePipeBuilder,
  UploadedFiles,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import { TakeoffService } from './takeoff.service';
import { CreateTakeoffDto } from './dto/create-takeoff.dto';
import { TakeoffStatus } from '../../prisma/app/generated/prisma/client';
import { UpdateTakeoffDto } from './dto/update-takeoff.dto';
import { PaginateQuery } from 'src/pagination/pagination.query';
import { TakeOffQueryDto } from './dto/take-off-query.dto';
import { FilesInterceptor } from '@nestjs/platform-express';

@Controller('takeoff')
export class TakeoffController {
  constructor(private readonly takeoffService: TakeoffService) {}

  @Post()
  @UseInterceptors(FilesInterceptor('files', 10))
  async create(
    @Req() req,
    @Body() createTakeoffDto: CreateTakeoffDto,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addMaxSizeValidator({
          maxSize: 1024 * 1024 * 50,
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    files: Array<Express.Multer.File>,
  ) {
    return this.takeoffService.create(req.user.id, createTakeoffDto, files);
  }

  @Get()
  async findAll(@Req() req, @Query() query: TakeOffQueryDto) {
    return this.takeoffService.findAll(req.user.id, query);
  }

  @Get(':id')
  async findOne(@Req() req, @Param('id') id: string) {
    return this.takeoffService.findOne(+id, req.user.id);
  }

  @Patch(':id')
  async update(
    @Req() req,
    @Param('id') id: string,
    @Body() updateTakeoffDto: UpdateTakeoffDto,
  ) {
    return this.takeoffService.update(+id, req.user.id, updateTakeoffDto);
  }

  @Patch(':id/status')
  async updateStatus(
    @Req() req,
    @Param('id') id: string,
    @Body('status') status: TakeoffStatus,
  ) {
    return this.takeoffService.updateStatus(+id, req.user.id, status);
  }

  @Delete(':id')
  async remove(@Req() req, @Param('id') id: string) {
    return this.takeoffService.softDelete(+id, req.user.id);
  }

  @Delete(':id/permanent')
  async hardDelete(@Req() req, @Param('id') id: string) {
    return this.takeoffService.hardDelete(+id, req.user.id);
  }
}
