import {
  IsDate,
  IsDecimal,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { FileUploadDto } from 'src/file-upload/dto/file-upload.dto';
import { BlueprintFileDto } from './blue-print-file.dto';
import { TakeoffStatus } from 'prisma/app/generated/prisma/client';

export class UpdateTakeoffDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  submissionDate?: Date;

  @IsNumber()
  @IsOptional()
  @Transform(({ value }) =>
    value !== undefined ? parseFloat(value) : undefined,
  )
  quotedPrice?: number;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  dueDate?: Date;

  @IsEnum(TakeoffStatus)
  @IsOptional()
  status?: TakeoffStatus;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => BlueprintFileDto)
  blueprintFiles?: BlueprintFileDto[];
}
