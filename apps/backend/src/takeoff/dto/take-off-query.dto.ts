import { IsEnum, IsIn, IsOptional, IsString } from 'class-validator';
import { TakeoffStatus } from 'prisma/app/generated/prisma/client';
import { PaginateQuery } from 'src/pagination/pagination.query';

export class TakeOffQueryDto extends PaginateQuery {
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  orderBySubmissionDate?: string = 'desc';

  @IsOptional()
  @IsString()
  @IsEnum(TakeoffStatus)
  status?: string;
}
