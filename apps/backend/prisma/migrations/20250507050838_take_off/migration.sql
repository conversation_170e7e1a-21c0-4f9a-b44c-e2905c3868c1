-- CreateTable
CREATE TABLE `takeoffs` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NULL,
    `description` TEXT NULL,
    `submissionDate` DATETIME(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
    `quotedPrice` DECIMAL(10, 2) NULL,
    `dueDate` DATETIME(3) NULL,
    `status` ENUM('submitted', 'won', 'lost') NOT NULL DEFAULT 'submitted',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `blueprint_files` (
    `id` VARCHAR(191) NOT NULL,
    `takeoffId` INTEGER NOT NULL,
    `fileName` VARCHAR(191) NULL,
    `fileUrl` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `takeoffs` ADD CONSTRAINT `takeoffs_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `blueprint_files` ADD CONSTRAINT `blueprint_files_takeoffId_fkey` FOREIGN KEY (`takeoffId`) REFERENCES `takeoffs`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
