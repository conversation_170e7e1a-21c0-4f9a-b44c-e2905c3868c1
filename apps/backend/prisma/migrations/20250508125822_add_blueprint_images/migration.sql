-- CreateTable
CREATE TABLE `BlueprintImage` (
    `id` VARCHAR(191) NOT NULL,
    `blueprintFileId` VARCHAR(191) NOT NULL,
    `filename` VARCHAR(191) NULL,
    `aws<PERSON>ey` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,

    INDEX `BlueprintImage_blueprintFileId_idx`(`blueprintFileId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `BlueprintImage` ADD CONSTRAINT `BlueprintImage_blueprintFileId_fkey` FOREIGN KEY (`blueprintFileId`) REFERENCES `blueprint_files`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
