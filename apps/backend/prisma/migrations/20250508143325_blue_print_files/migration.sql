/*
  Warnings:

  - You are about to drop the `BlueprintImage` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Organization` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `BlueprintImage` DROP FOREIGN KEY `BlueprintImage_blueprintFileId_fkey`;

-- DropForeignKey
ALTER TABLE `Organization` DROP FOREIGN KEY `Organization_userId_fkey`;

-- DropTable
DROP TABLE `BlueprintImage`;

-- DropTable
DROP TABLE `Organization`;

-- CreateTable
CREATE TABLE `organizations` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `organizations_userId_key`(`userId`),
    <PERSON><PERSON><PERSON><PERSON>EY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `blueprint_images` (
    `id` VARCHAR(191) NOT NULL,
    `blueprintFileId` VARCHAR(191) NOT NULL,
    `filename` VARCHAR(191) NULL,
    `awsKey` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,

    INDEX `blueprint_images_blueprintFileId_idx`(`blueprintFileId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `organizations` ADD CONSTRAINT `organizations_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `blueprint_images` ADD CONSTRAINT `blueprint_images_blueprintFileId_fkey` FOREIGN KEY (`blueprintFileId`) REFERENCES `blueprint_files`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
