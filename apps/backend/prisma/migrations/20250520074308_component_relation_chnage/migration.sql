/*
  Warnings:

  - You are about to drop the column `blueprintImageId` on the `components` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE `components` DROP FOREIGN KEY `components_blueprintImageId_fkey`;

-- DropIndex
DROP INDEX `components_blueprintImageId_fkey` ON `components`;

-- AlterTable
ALTER TABLE `components` DROP COLUMN `blueprintImageId`;

-- CreateTable
CREATE TABLE `_BlueprintImageToComponent` (
    `A` VARCHAR(191) NOT NULL,
    `B` INTEGER NOT NULL,

    UNIQUE INDEX `_BlueprintImageToComponent_AB_unique`(`A`, `B`),
    INDEX `_BlueprintImageToComponent_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `_BlueprintImageToComponent` ADD CONSTRAINT `_BlueprintImageToComponent_A_fkey` F<PERSON><PERSON><PERSON><PERSON> KEY (`A`) REFERENCES `blueprint_images`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_BlueprintImageToComponent` ADD CONSTRAINT `_BlueprintImageToComponent_B_fkey` FOREIGN KEY (`B`) REFERENCES `components`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
