-- CreateTable
CREATE TABLE `drawings` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `blueprintImageId` VARCHAR(191) NOT NULL,
    `componentId` INTEGER NULL,
    `config` <PERSON><PERSON><PERSON> NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add<PERSON><PERSON><PERSON>K<PERSON>
ALTER TABLE `drawings` ADD CONSTRAINT `drawings_blueprintImageId_fkey` FOREIGN KEY (`blueprintImageId`) REFERENCES `blueprint_images`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE `drawings` ADD CONSTRAINT `drawings_componentId_fkey` FOREIGN KEY (`componentId`) REFERENCES `components`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
