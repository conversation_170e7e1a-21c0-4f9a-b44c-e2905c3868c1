-- CreateTable
CREATE TABLE `components` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VA<PERSON>HAR(191) NOT NULL,
    `blueprintFileId` VARCHAR(191) NOT NULL,
    `blueprintImageId` VARCHAR(191) NOT NULL,
    `geometryType` ENUM('surface', 'edge', 'point') NULL,
    `description` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `components` ADD CONSTRAINT `components_blueprintFileId_fkey` FOREIGN KEY (`blueprintFileId`) REFERENCES `blueprint_files`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON>ign<PERSON><PERSON>
ALTER TABLE `components` ADD CONSTRAINT `components_blueprintImageId_fkey` FOREIGN KEY (`blueprintImageId`) REFERENCES `blueprint_images`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
