generator client {
  provider = "prisma-client-js"
  output   = "app/generated/prisma/client"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum UserStatus {
  active
  pending
  inactive
}

model User {
  id                    String             @id @default(uuid())
  fullName              String?
  email                 String?            @unique
  password              String
  phoneNumber           String             @unique
  phoneNumberVerifiedAt DateTime?
  emailVerifiedAt       DateTime?
  profilePic            Json?
  resetToken            String?
  resetTokenExpiresAt   DateTime?
  refreshToken          String?
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  deletedAt             DateTime?
  status                UserStatus         @default(pending)
  roles                 Role[]
  verifications         UserVerification[]
  organization          Organization?
  uploadedFiles         File[]
  Takeoffs              Takeoff[]

  @@map("users")
}

enum VerificationType {
  SMS
  EMAIL
}

model UserVerification {
  id               String           @id @default(uuid())
  userId           String
  verificationType VerificationType
  code             String
  expiresAt        DateTime
  verifiedAt       DateTime?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("user_verifications")
}

model Role {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  users User[]

  @@map("roles")
}

model Organization {
  id          Int      @id @default(autoincrement())
  userId      String   @unique
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  owner User @relation(fields: [userId], references: [id])

  @@map("organizations")
}

enum FileType {
  image
  video
  audio
  document
}

enum AccessType {
  public
  private
}

model File {
  id          Int        @id @default(autoincrement())
  key         String     @db.Text
  userId      String?
  path        String     @db.Text
  file_type   FileType
  access_type AccessType
  created_at  DateTime   @default(now())
  updated_at  DateTime   @updatedAt
  deleted_at  DateTime?

  owner User? @relation(fields: [userId], references: [id])

  @@map("files")
}

enum TakeoffStatus {
  submitted
  won
  lost
}

model Takeoff {
  id             Int           @id @default(autoincrement())
  userId         String
  name           String?
  description    String?       @db.Text
  submissionDate DateTime?     @default(now())
  quotedPrice    Decimal?      @db.Decimal(10, 2)
  dueDate        DateTime?
  status         TakeoffStatus @default(submitted)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  deletedAt      DateTime?

  creator        User             @relation(fields: [userId], references: [id])
  blueprintFiles BlueprintFiles[]

  @@map("takeoffs")
}

model BlueprintFiles {
  id        String    @id @default(uuid())
  takeoffId Int
  fileName  String?
  fileUrl   String?   @db.Text
  awsKey    String?   @db.Text
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  takeoff         Takeoff          @relation(fields: [takeoffId], references: [id])
  blueprintImages BlueprintImage[]
  components      Component[] // Direct relation to components

  @@map("blueprint_files")
}

model BlueprintImage {
  id              String    @id @default(uuid())
  blueprintFileId String
  filename        String?   @db.Text
  path            String?   @db.Text
  pageNumber      Int?
  scale           Json?
  dimensions      Json?
  awsKey          String?   @db.Text
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  deletedAt       DateTime?

  blueprintFile BlueprintFiles @relation(fields: [blueprintFileId], references: [id])
  components    Component[] // Many-to-many relation with components
  drawings      Drawing[]

  @@index([blueprintFileId])
  @@map("blueprint_images")
}

enum GeometryType {
  surface
  edge
  point
}

enum SelectionType {
  allPages
  currentPage
}

model Component {
  id              Int            @id @default(autoincrement())
  name            String
  blueprintFileId String // Direct relation to blueprint file
  geometryType    GeometryType?
  geometricData   Json?
  color           String?
  shade           String?
  description     String?        @db.Text
  selectionType   SelectionType? @default(allPages)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  deletedAt       DateTime?

  blueprintFile   BlueprintFiles   @relation(fields: [blueprintFileId], references: [id])
  blueprintImages BlueprintImage[] // Many-to-many relation with images
  drawings        Drawing[]

  @@map("components")
}

model Drawing {
  id               Int       @id @default(autoincrement())
  blueprintImageId String
  componentId      Int?
  config           Json?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  deletedAt        DateTime?

  blueprintImage BlueprintImage @relation(fields: [blueprintImageId], references: [id])
  component      Component?     @relation(fields: [componentId], references: [id])

  @@map("drawings")
}
