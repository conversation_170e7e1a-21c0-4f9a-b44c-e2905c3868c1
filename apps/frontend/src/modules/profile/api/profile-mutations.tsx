import { IGetMe } from '@/api/get-me';
import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

interface ChangePasswordPayload {
  current_password: string;
  new_password: string;
}

const changePassword = async (payload: ChangePasswordPayload) => {
  return apiClient.post(`/auth/change-password`, payload);
};

export const useChangePassword = () => {
  return useMutation({
    mutationFn: changePassword,
  });
};

type UpdateProfilePayload = {
  fullName: string;
  email: string;
  phoneNumber: string;
  profilePic?: ProfilePic;
};

type ProfilePic = {
  id: number;
  file_url: string;
  //  file_name: string;
  //  key: string;
  //  file_type: string;
  //  access_type: string;
};

const updateProfile = async (
  payload: UpdateProfilePayload,
): Promise<{ data: IGetMe }> => {
  return apiClient.patch(`/users/profile`, payload);
};

export const useUpdateProfile = () => {
  return useMutation({
    mutationFn: updateProfile,
  });
};
