'use client';

import type React from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeftIcon, Eye, EyeOff, Loader2, Pencil } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { useGetMe } from '@/api/get-me';
import { useDeleteFile, useUploadFile } from '@/api/s3-operations';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from '@/lib/toast';
import { useAuthStore } from '@/store/auth-store';
import { useQueryClient } from '@tanstack/react-query';
import { useChangePassword, useUpdateProfile } from './api/profile-mutations';
import { useRouter } from 'next/navigation';

// Profile form schema
const profileFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  mobile: z.string().min(10, { message: 'Please enter a valid phone number.' }),
  organization: z
    .string()
    .min(2, { message: 'Name must be at least 2 characters.' }),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

// Security form schema
const securityFormSchema = z
  .object({
    currentPassword: z
      .string()
      .min(1, { message: 'Current password is required.' }),
    newPassword: z
      .string()
      .min(8, { message: 'Password must be at least 8 characters.' }),
    confirmPassword: z
      .string()
      .min(8, { message: 'Password must be at least 8 characters.' }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match.',
    path: ['confirmPassword'],
  });

type SecurityFormValues = z.infer<typeof securityFormSchema>;

export function ProfilePage() {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [profileImage, setProfileImage] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const changePassword = useChangePassword();
  const updateProfile = useUpdateProfile();
  const authStore = useAuthStore();
  const userId = authStore.userId;
  const { data: user } = useGetMe();
  const uploadFile = useUploadFile();
  const deleteFile = useDeleteFile();
  const queryClient = useQueryClient();
  const router = useRouter();

  // Profile form
  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: user?.fullName || '',
      email: user?.email || '',
      mobile: user?.phoneNumber || '',
      organization: user?.organization?.name || '',
    },
  });

  useEffect(() => {
    if (user) {
      setProfileImage(user?.profilePic?.file_url || '');
    }
  }, [user]);

  useEffect(() => {
    profileForm.reset({
      name: user?.fullName || '',
      email: user?.email || '',
      mobile: user?.phoneNumber || '',
      organization: user?.organization?.name || '',
    });
  }, [user, profileForm]);

  // Security form
  const securityForm = useForm<SecurityFormValues>({
    resolver: zodResolver(securityFormSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setProfileImage(imageUrl);
    }
  };

  const handlePhotoClick = () => {
    fileInputRef.current?.click();
  };

  const onProfileSubmit = async (data: ProfileFormValues) => {
    let profilePicture = user?.profilePic;
    const file = fileInputRef.current?.files?.[0];
    if (file) {
      await uploadFile.mutateAsync(
        { file },
        {
          onSuccess: (data) => {
            if (fileInputRef.current) {
              fileInputRef.current.value = '';
            }
            setProfileImage(data.file_url);
            profilePicture = { id: data.id, file_url: data.file_url };

            if (user?.profilePic) {
              deleteFile.mutate({
                file_id: user?.profilePic.id,
              });
            }
          },
        },
      );
    }

    updateProfile.mutate(
      {
        fullName: data.name,
        email: data.email,
        ...(profilePicture && { profilePic: profilePicture }),
        phoneNumber: data.mobile,
      },
      {
        onSuccess: () => {
          toast.success('Profile updated', {
            description:
              'Your profile information has been updated successfully.',
          });

          queryClient.invalidateQueries({
            queryKey: ['userProfile', userId],
          });
        },
        onError: () => {
          toast.error('Profile update failed', {
            description: 'Please try again.',
          });
        },
      },
    );
  };

  const onSecuritySubmit = (data: SecurityFormValues) => {
    if (!userId) {
      return toast.error('User not found', {
        description: 'Please try again.',
      });
    }

    changePassword.mutate(
      {
        current_password: data.currentPassword,
        new_password: data.newPassword,
      },
      {
        onSuccess: () => {
          toast.success('Password changed', {
            description: 'Your password has been changed successfully.',
          });
          securityForm.reset();
        },
        onError: (error) => {
          toast.error(error);
        },
      },
    );
  };

  const profilePending =
    profileForm.formState.isSubmitting ||
    updateProfile.isPending ||
    deleteFile.isPending ||
    uploadFile.isPending;

  return (
    <div className="px-4 lg:px-8 xl:px-8 py-2 lg:py-6">
      <div className="flex items-center gap-2 mb-2">
        <ArrowLeftIcon
          onClick={() => router.back()}
          className="h-4 w-4 cursor-pointer"
        />
        <h1 className="text-xl font-semibold">Profile Settings</h1>
      </div>

      <p className="text-muted-foreground mb-4">
        Manage your account settings and security preferences.
      </p>

      <div className="flex flex-col lg:flex-row gap-4 w-full">
        {/* Personal Information */}
        <Card className="w-full lg:w-3/4 xl:w-2/4">
          <CardHeader>
            <CardTitle className="text-lg">Your Profile</CardTitle>
            <CardDescription className="text-sm">
              Update your personal information
            </CardDescription>
          </CardHeader>
          <Form {...profileForm}>
            <form onSubmit={profileForm.handleSubmit(onProfileSubmit)}>
              <CardContent className="pt-6">
                <div className="flex flex-col lg:flex-row gap-8">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="relative">
                      <Avatar
                        onClick={handlePhotoClick}
                        className="h-32 w-32 cursor-pointer"
                      >
                        <AvatarImage src={profileImage} alt="Profile picture" />
                        <AvatarFallback>
                          {user?.fullName?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <Button
                        size="icon"
                        variant="secondary"
                        className="absolute bottom-0 right-0 h-8 w-8 rounded-full cursor-pointer"
                        onClick={handlePhotoClick}
                        type="button"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <input
                        type="file"
                        ref={fileInputRef}
                        accept="image/*"
                        className="hidden"
                        onChange={handleFileChange}
                        aria-label="Upload profile picture"
                      />
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-sm text-muted-foreground h-auto p-0 cursor-pointer"
                      onClick={handlePhotoClick}
                      type="button"
                    >
                      Change photo
                    </Button>
                  </div>

                  <div className="flex-1 grid gap-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <FormField
                        control={profileForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel>Full Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={profileForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                className="bg-muted cursor-not-allowed"
                                readOnly
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <FormField
                        control={profileForm.control}
                        name="mobile"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                className="bg-muted cursor-not-allowed"
                                readOnly
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={profileForm.control}
                        name="organization"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel>Organisation</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Enter your organisation"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end pt-6">
                <Button disabled={profilePending} type="submit">
                  {profilePending ? 'Saving...' : 'Save changes'}
                  {profilePending && (
                    <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                  )}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </Card>

        {/* Security Section */}
        <Card className="w-full lg:w-3/4 xl:w-2/4">
          <CardHeader>
            <CardTitle className="text-lg">Security</CardTitle>
            <CardDescription className="text-sm">
              Update your password to keep your account secure
            </CardDescription>
          </CardHeader>
          <Form {...securityForm}>
            <form onSubmit={securityForm.handleSubmit(onSecuritySubmit)}>
              <CardContent className="pt-6 space-y-6">
                <div className="grid grid-cols-1 gap-4">
                  <FormField
                    control={securityForm.control}
                    name="currentPassword"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel>Current password</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              {...field}
                              type={showCurrentPassword ? 'text' : 'password'}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              setShowCurrentPassword(!showCurrentPassword)
                            }
                            className="absolute right-0 top-0 h-full px-3 text-muted-foreground"
                          >
                            {showCurrentPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 relative top-[1px]">
                  <FormField
                    control={securityForm.control}
                    name="newPassword"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel>New password</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              {...field}
                              type={showNewPassword ? 'text' : 'password'}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            className="absolute right-0 top-0 h-full px-3 text-muted-foreground"
                          >
                            {showNewPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={securityForm.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel>Confirm password</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              {...field}
                              type={showConfirmPassword ? 'text' : 'password'}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              setShowConfirmPassword(!showConfirmPassword)
                            }
                            className="absolute right-0 top-0 h-full px-3 text-muted-foreground"
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-end pt-6">
                <Button
                  className="relative top-[4px]"
                  disabled={changePassword.isPending}
                  type="submit"
                >
                  {changePassword.isPending
                    ? 'Changing Password...'
                    : 'Change Password'}
                  {changePassword.isPending && (
                    <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                  )}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </Card>
      </div>
    </div>
  );
}
