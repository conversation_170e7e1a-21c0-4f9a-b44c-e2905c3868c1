import { CanvasPosition } from '../types/drawing-types';

/**
 * Ease-out quartic easing function for smooth animations
 * @param t Progress value between 0 and 1
 * @returns Eased value between 0 and 1
 */
export function easeOutQuart(t: number): number {
  return 1 - Math.pow(1 - t, 4);
}

/**
 * Animates position from one point to another with smooth easing
 * @param from Starting position
 * @param to Target position
 * @param duration Animation duration in milliseconds
 * @param onUpdate Callback called on each frame with current position
 * @param onComplete Optional callback called when animation completes
 * @returns Cancel function to stop the animation
 */
export function animatePosition(
  from: CanvasPosition,
  to: CanvasPosition,
  duration: number,
  onUpdate: (position: CanvasPosition) => void,
  onComplete?: () => void,
): () => void {
  const startTime = performance.now();
  let animationId: number;
  let cancelled = false;

  const animate = (currentTime: number) => {
    if (cancelled) return;

    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const easedProgress = easeOutQuart(progress);

    // Interpolate between start and end positions
    const currentPosition: CanvasPosition = {
      x: from.x + (to.x - from.x) * easedProgress,
      y: from.y + (to.y - from.y) * easedProgress,
    };

    onUpdate(currentPosition);

    if (progress < 1) {
      animationId = requestAnimationFrame(animate);
    } else {
      onComplete?.();
    }
  };

  animationId = requestAnimationFrame(animate);

  // Return cancel function
  return () => {
    cancelled = true;
    if (animationId) {
      cancelAnimationFrame(animationId);
    }
  };
}

/**
 * Calculates the distance between two positions
 * @param pos1 First position
 * @param pos2 Second position
 * @returns Distance between positions
 */
export function getPositionDistance(
  pos1: CanvasPosition,
  pos2: CanvasPosition,
): number {
  const dx = pos2.x - pos1.x;
  const dy = pos2.y - pos1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Checks if two positions are approximately equal within a threshold
 * @param pos1 First position
 * @param pos2 Second position
 * @param threshold Maximum difference to consider equal (default: 1)
 * @returns True if positions are approximately equal
 */
export function positionsEqual(
  pos1: CanvasPosition,
  pos2: CanvasPosition,
  threshold = 1,
): boolean {
  return getPositionDistance(pos1, pos2) <= threshold;
}
