/**
 * Utility functions to generate point arrays for different point shapes
 * All shapes are designed to be used with Line components with closed=true
 */

import { POINT_SIZES, DRAWING_BEHAVIOR } from '../constants/drawing';

/**
 * Generate points for a circle shape (approximated as an octagon)
 * @param centerX - X coordinate of the center
 * @param centerY - Y coordinate of the center
 * @param radius - Radius of the circle (optional, uses default if not provided)
 * @returns Array of points [x1, y1, x2, y2, ...] forming a circle
 */
export const generateCirclePoints = (
  centerX: number,
  centerY: number,
  radius: number = POINT_SIZES.CIRCLE,
): number[] => {
  const points: number[] = [];
  const sides = DRAWING_BEHAVIOR.CIRCLE_APPROXIMATION_SIDES;

  for (let i = 0; i < sides; i++) {
    const angle = (i * 2 * Math.PI) / sides;
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);
    points.push(x, y);
  }

  return points;
};

/**
 * Generate points for a square shape
 * @param centerX - X coordinate of the center
 * @param centerY - Y coordinate of the center
 * @param size - Size of the square (half-width from center to edge, optional, uses default if not provided)
 * @returns Array of points [x1, y1, x2, y2, ...] forming a square
 */
export const generateSquarePoints = (
  centerX: number,
  centerY: number,
  size: number = POINT_SIZES.SQUARE,
): number[] => {
  return [
    centerX - size,
    centerY - size, // Top-left
    centerX + size,
    centerY - size, // Top-right
    centerX + size,
    centerY + size, // Bottom-right
    centerX - size,
    centerY + size, // Bottom-left
  ];
};

/**
 * Generate points for a triangle shape (equilateral triangle pointing up)
 * @param centerX - X coordinate of the center
 * @param centerY - Y coordinate of the center
 * @param size - Size of the triangle (radius of circumscribed circle, optional, uses default if not provided)
 * @returns Array of points [x1, y1, x2, y2, ...] forming a triangle
 */
export const generateTrianglePoints = (
  centerX: number,
  centerY: number,
  size: number = POINT_SIZES.TRIANGLE,
): number[] => {
  const height = (size * Math.sqrt(3)) / 2;

  return [
    centerX,
    centerY - size, // Top point
    centerX + height,
    centerY + size / 2, // Bottom-right
    centerX - height,
    centerY + size / 2, // Bottom-left
  ];
};

/**
 * Get the appropriate default size for a point type
 * @param pointType - Type of point shape
 * @returns Default size for the point type
 */
export const getPointSize = (
  pointType: 'circle' | 'square' | 'triangle',
): number => {
  switch (pointType) {
    case 'circle':
      return POINT_SIZES.CIRCLE;
    case 'square':
      return POINT_SIZES.SQUARE;
    case 'triangle':
      return POINT_SIZES.TRIANGLE;
    default:
      return POINT_SIZES.CIRCLE; // Default fallback
  }
};

/**
 * Get point shape generator function based on point type
 * @param pointType - Type of point shape to generate
 * @returns Function to generate points for the specified shape
 */
export const getPointShapeGenerator = (
  pointType: 'circle' | 'square' | 'triangle',
) => {
  switch (pointType) {
    case 'circle':
      return generateCirclePoints;
    case 'square':
      return generateSquarePoints;
    case 'triangle':
      return generateTrianglePoints;
    default:
      return generateCirclePoints; // Default fallback
  }
};
