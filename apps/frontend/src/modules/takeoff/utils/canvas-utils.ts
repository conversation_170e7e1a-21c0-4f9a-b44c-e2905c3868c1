import Konva from 'konva';
import { CanvasPosition } from '../types/drawing-types';
import { v4 as uuidv4 } from 'uuid';
import { DRAWING_COLORS } from '../constants/drawing';

/**
 * Generates a unique ID for a drawing shape using uuid
 */
export const generateId = (): string => `shape-${uuidv4()}`;

/**
 * Gets the relative pointer position on the stage, accounting for scale and position
 */
export const getRelativePointerPosition = (
  stage: Konva.Stage | null,
  position: CanvasPosition,
  scale: number,
): { x: number; y: number } | null => {
  if (!stage) return null;

  const pointerPosition = stage.getPointerPosition();
  if (!pointerPosition) return null;

  const result = {
    x: (pointerPosition.x - position.x) / scale,
    y: (pointerPosition.y - position.y) / scale,
  };

  return result;
};

/**
 * Formats a color with alpha transparency
 */
export const formatColorWithAlpha = (
  color: string | undefined,
  alpha: string = '80',
): string => {
  return color ? `${color}${alpha}` : '';
};

/**
 * Calculates the radius of a circle based on two points
 */
export const calculateRadius = (
  x1: number,
  y1: number,
  x2: number,
  y2: number,
): number => {
  const dx = x2 - x1;
  const dy = y2 - y1;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * Calculates the radii of an ellipse based on two points
 */
export const calculateEllipseRadii = (
  x1: number,
  y1: number,
  x2: number,
  y2: number,
): { radiusX: number; radiusY: number } => {
  const radiusX = Math.abs(x2 - x1);
  const radiusY = Math.abs(y2 - y1);
  return { radiusX, radiusY };
};

/**
 * Makes a color lighter for display purposes.
 * Handles both hex colors and colors with alpha values.
 * @param color - The color to lighten (hex or with alpha).
 * @returns A lighter version of the color.
 */
export const makeLighterForDisplay = (color: string | undefined): string => {
  if (!color) return DRAWING_COLORS.DEFAULT_STROKE;

  // For colors with alpha (ending with '80'), replace with '50'
  if (color.endsWith('80')) {
    return color.replace('80', '50');
  }

  // For hex colors, add 50% opacity
  if (color.startsWith('#')) {
    return `${color}80`;
  }

  // Return the original color if no transformation applies
  return color;
};

/**
 * Makes a color brighter for border display purposes.
 * Creates a more vibrant version of the original color for borders.
 * @param color - The original color (hex or with alpha).
 * @returns A brighter version of the color for borders.
 */
export const makeBrighterForBorder = (color: string | undefined): string => {
  if (!color) return DRAWING_COLORS.DEFAULT_STROKE;

  // Remove any existing alpha values to work with the base color
  let baseColor = color;
  if (color.endsWith('80') || color.endsWith('50')) {
    baseColor = color.slice(0, -2);
  }

  // If it's a hex color, make it brighter by increasing the RGB values
  if (baseColor.startsWith('#')) {
    const hex = baseColor.slice(1);

    // Handle both 3-digit and 6-digit hex colors
    let r, g, b;
    if (hex.length === 3) {
      r = parseInt(hex[0] + hex[0], 16);
      g = parseInt(hex[1] + hex[1], 16);
      b = parseInt(hex[2] + hex[2], 16);
    } else if (hex.length === 6) {
      r = parseInt(hex.slice(0, 2), 16);
      g = parseInt(hex.slice(2, 4), 16);
      b = parseInt(hex.slice(4, 6), 16);
    } else {
      // Fallback to original color if invalid hex
      return baseColor;
    }

    // Increase brightness by 30% but cap at 255
    r = Math.min(255, Math.round(r * 1.3));
    g = Math.min(255, Math.round(g * 1.3));
    b = Math.min(255, Math.round(b * 1.3));

    // Convert back to hex
    const brighterHex = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b
      .toString(16)
      .padStart(2, '0')}`;

    // Add some opacity for a nice effect
    return `${brighterHex}CC`; // 80% opacity
  }

  // Return the original color if no transformation applies
  return baseColor;
};

/**
 * Checks if a drawing is inside a selection box
 * @param drawing - The drawing to check
 * @param selectionBox - The selection box
 * @returns True if the drawing is inside the selection box
 */
export const isDrawingInSelectionBox = (
  drawing: {
    x: number;
    y: number;
    width?: number;
    height?: number;
    radius?: number;
    radiusX?: number;
    radiusY?: number;
    points?: number[];
  },
  selectionBox: { x: number; y: number; width: number; height: number },
): boolean => {
  // Normalize selection box coordinates (handle negative width/height)
  const selectionLeft = Math.min(
    selectionBox.x,
    selectionBox.x + selectionBox.width,
  );
  const selectionRight = Math.max(
    selectionBox.x,
    selectionBox.x + selectionBox.width,
  );
  const selectionTop = Math.min(
    selectionBox.y,
    selectionBox.y + selectionBox.height,
  );
  const selectionBottom = Math.max(
    selectionBox.y,
    selectionBox.y + selectionBox.height,
  );

  // Add small tolerance for edge cases (helps with floating point precision)
  const tolerance = 1;

  if (drawing.radius !== undefined) {
    // Circle drawing - check if circle center is inside selection box
    const centerX = drawing.x;
    const centerY = drawing.y;

    return (
      centerX >= selectionLeft - tolerance &&
      centerX <= selectionRight + tolerance &&
      centerY >= selectionTop - tolerance &&
      centerY <= selectionBottom + tolerance
    );
  } else if (drawing.radiusX !== undefined && drawing.radiusY !== undefined) {
    // Ellipse drawing - check if ellipse center is inside selection box
    const centerX = drawing.x;
    const centerY = drawing.y;

    return (
      centerX >= selectionLeft - tolerance &&
      centerX <= selectionRight + tolerance &&
      centerY >= selectionTop - tolerance &&
      centerY <= selectionBottom + tolerance
    );
  } else if (drawing.width !== undefined && drawing.height !== undefined) {
    // Rectangle drawing - normalize rectangle coordinates too
    const drawingLeft = Math.min(drawing.x, drawing.x + drawing.width);
    const drawingRight = Math.max(drawing.x, drawing.x + drawing.width);
    const drawingTop = Math.min(drawing.y, drawing.y + drawing.height);
    const drawingBottom = Math.max(drawing.y, drawing.y + drawing.height);

    // Check if rectangles overlap (with tolerance)
    return (
      drawingLeft <= selectionRight + tolerance &&
      drawingRight >= selectionLeft - tolerance &&
      drawingTop <= selectionBottom + tolerance &&
      drawingBottom >= selectionTop - tolerance
    );
  } else if (drawing.points !== undefined && drawing.points.length >= 2) {
    // Freehand drawing - check if any point is inside the selection box
    for (let i = 0; i < drawing.points.length; i += 2) {
      const pointX = drawing.points[i];
      const pointY = drawing.points[i + 1];

      if (
        pointX >= selectionLeft - tolerance &&
        pointX <= selectionRight + tolerance &&
        pointY >= selectionTop - tolerance &&
        pointY <= selectionBottom + tolerance
      ) {
        return true;
      }
    }
  }

  return false;
};

/**
 * Checks if a drawing is within the canvas bounds (now that image fills entire canvas)
 * @param drawing - Drawing object with position and dimensions
 * @param canvasBounds - Canvas dimensions (same as image bounds now)
 * @returns true if drawing is completely within bounds
 */
export const isDrawingWithinBounds = (
  drawing: {
    x: number;
    y: number;
    width?: number;
    height?: number;
    radius?: number;
    radiusX?: number;
    radiusY?: number;
    points?: number[];
    type?: string;
  },
  canvasBounds: { width: number; height: number },
): boolean => {
  // Handle comments specifically - they only need their anchor point to be within bounds
  if (drawing.type === 'comment') {
    const withinBounds =
      drawing.x >= 0 &&
      drawing.x <= canvasBounds.width &&
      drawing.y >= 0 &&
      drawing.y <= canvasBounds.height;
    return withinBounds;
  }

  if (drawing.radius !== undefined) {
    // Circle drawing
    const left = drawing.x - drawing.radius;
    const right = drawing.x + drawing.radius;
    const top = drawing.y - drawing.radius;
    const bottom = drawing.y + drawing.radius;

    return (
      left >= 0 &&
      right <= canvasBounds.width &&
      top >= 0 &&
      bottom <= canvasBounds.height
    );
  } else if (drawing.radiusX !== undefined && drawing.radiusY !== undefined) {
    // Ellipse drawing
    const left = drawing.x - drawing.radiusX;
    const right = drawing.x + drawing.radiusX;
    const top = drawing.y - drawing.radiusY;
    const bottom = drawing.y + drawing.radiusY;

    return (
      left >= 0 &&
      right <= canvasBounds.width &&
      top >= 0 &&
      bottom <= canvasBounds.height
    );
  } else if (drawing.width !== undefined && drawing.height !== undefined) {
    // Rectangle drawing - handle negative dimensions
    const left = Math.min(drawing.x, drawing.x + drawing.width);
    const right = Math.max(drawing.x, drawing.x + drawing.width);
    const top = Math.min(drawing.y, drawing.y + drawing.height);
    const bottom = Math.max(drawing.y, drawing.y + drawing.height);

    return (
      left >= 0 &&
      right <= canvasBounds.width &&
      top >= 0 &&
      bottom <= canvasBounds.height
    );
  } else if (drawing.points !== undefined && drawing.points.length >= 2) {
    // Freehand drawing - check all points
    for (let i = 0; i < drawing.points.length; i += 2) {
      const x = drawing.points[i];
      const y = drawing.points[i + 1];
      if (x < 0 || x > canvasBounds.width || y < 0 || y > canvasBounds.height) {
        return false;
      }
    }
    return true;
  }

  return false;
};

/**
 * Checks if multiple drawings are all within the canvas bounds (now that image fills entire canvas)
 * @param drawings - Array of drawings to check
 * @param canvasBounds - Canvas dimensions (same as image bounds now)
 * @returns true if all drawings are completely within bounds
 */
export const areAllDrawingsWithinBounds = (
  drawings: Array<{
    x: number;
    y: number;
    width?: number;
    height?: number;
    radius?: number;
    radiusX?: number;
    radiusY?: number;
    points?: number[];
    type?: string;
  }>,
  canvasBounds: { width: number; height: number },
): boolean => {
  return drawings.every((drawing) =>
    isDrawingWithinBounds(drawing, canvasBounds),
  );
};

/**
 * Calculates the bounding box of multiple drawings
 * @param drawings - Array of drawings to calculate bounds for
 * @returns Bounding box coordinates
 */
export const calculateSelectionBounds = (
  drawings: Array<{
    x: number;
    y: number;
    width?: number;
    height?: number;
    radius?: number;
    radiusX?: number;
    radiusY?: number;
    points?: number[];
  }>,
): { x: number; y: number; width: number; height: number } | null => {
  if (drawings.length === 0) return null;

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  drawings.forEach((drawing) => {
    if (drawing.radius !== undefined) {
      // Circle drawing
      const centerX = drawing.x;
      const centerY = drawing.y;
      const radius = drawing.radius;

      minX = Math.min(minX, centerX - radius);
      maxX = Math.max(maxX, centerX + radius);
      minY = Math.min(minY, centerY - radius);
      maxY = Math.max(maxY, centerY + radius);
    } else if (drawing.radiusX !== undefined && drawing.radiusY !== undefined) {
      // Ellipse drawing
      const centerX = drawing.x;
      const centerY = drawing.y;
      const radiusX = drawing.radiusX;
      const radiusY = drawing.radiusY;

      minX = Math.min(minX, centerX - radiusX);
      maxX = Math.max(maxX, centerX + radiusX);
      minY = Math.min(minY, centerY - radiusY);
      maxY = Math.max(maxY, centerY + radiusY);
    } else if (drawing.width !== undefined && drawing.height !== undefined) {
      // Rectangle drawing - handle negative dimensions
      const left = Math.min(drawing.x, drawing.x + drawing.width);
      const right = Math.max(drawing.x, drawing.x + drawing.width);
      const top = Math.min(drawing.y, drawing.y + drawing.height);
      const bottom = Math.max(drawing.y, drawing.y + drawing.height);

      minX = Math.min(minX, left);
      maxX = Math.max(maxX, right);
      minY = Math.min(minY, top);
      maxY = Math.max(maxY, bottom);
    } else if (drawing.points !== undefined && drawing.points.length >= 2) {
      // Freehand drawing - check all points
      for (let i = 0; i < drawing.points.length; i += 2) {
        const pointX = drawing.points[i];
        const pointY = drawing.points[i + 1];

        minX = Math.min(minX, pointX);
        maxX = Math.max(maxX, pointX);
        minY = Math.min(minY, pointY);
        maxY = Math.max(maxY, pointY);
      }
    } else {
      // Handle other drawing types (like comments) - use their x,y position
      // For comments, we'll use the position and a default width (since they have a fixed width)
      minX = Math.min(minX, drawing.x);
      maxX = Math.max(maxX, drawing.x + (drawing.width || 200)); // Comments have a fixed width of 200px
      minY = Math.min(minY, drawing.y);
      maxY = Math.max(maxY, drawing.y + 50); // Estimate a height for comments
    }
  });

  // Add some padding around the selection
  const padding = 10;
  return {
    x: minX - padding,
    y: minY - padding,
    width: maxX - minX + 2 * padding,
    height: maxY - minY + 2 * padding,
  };
};

/**
 * Calculates the actual image bounds within the canvas
 * This replicates the logic from URLImage component
 * @param imageElement - The loaded image element
 * @param canvasWidth - Canvas width
 * @param canvasHeight - Canvas height
 * @returns Image bounds with position and dimensions
 */
export const calculateImageBounds = (
  imageElement: HTMLImageElement | null,
  canvasWidth: number,
  canvasHeight: number,
): { x: number; y: number; width: number; height: number } => {
  if (!imageElement) {
    // If no image, return canvas bounds as fallback
    return { x: 0, y: 0, width: canvasWidth, height: canvasHeight };
  }

  const imageWidth = imageElement.width;
  const imageHeight = imageElement.height;

  // Center the image on the canvas (same logic as URLImage)
  const x = Math.max(0, (canvasWidth - imageWidth) / 2);
  const y = Math.max(0, (canvasHeight - imageHeight) / 2);

  return {
    x,
    y,
    width: imageWidth,
    height: imageHeight,
  };
};

/**
 * Checks if a point is inside a bounding box
 * @param point - The point to check
 * @param bounds - The bounding box
 * @returns True if the point is inside the bounding box
 */
export const isPointInBounds = (
  point: { x: number; y: number },
  bounds: { x: number; y: number; width: number; height: number },
): boolean => {
  return (
    point.x >= bounds.x &&
    point.x <= bounds.x + bounds.width &&
    point.y >= bounds.y &&
    point.y <= bounds.y + bounds.height
  );
};

/**
 * Line segment interface for intersection detection
 */
export interface LineSegment {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

/**
 * Checks if two line segments intersect
 * Uses parametric line equations for accurate intersection detection
 * @param line1 - First line segment
 * @param line2 - Second line segment
 * @returns True if the line segments intersect
 */
export const doLinesIntersect = (
  line1: LineSegment,
  line2: LineSegment,
): boolean => {
  const { x1, y1, x2, y2 } = line1;
  const { x1: x3, y1: y3, x2: x4, y2: y4 } = line2;

  const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);

  // Lines are parallel if denominator is zero (with small tolerance for floating point)
  if (Math.abs(denom) < 1e-10) {
    return false;
  }

  const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
  const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

  // Use a small epsilon to avoid endpoint touching being considered intersection
  const epsilon = 1e-10;

  // Intersection occurs if both parameters are strictly between 0 and 1 (excluding endpoints)
  return t > epsilon && t < 1 - epsilon && u > epsilon && u < 1 - epsilon;
};

/**
 * Checks if a freehand drawing has self-intersecting line segments
 * Optimized to only check recent segments for performance
 * @param points - Array of points [x1, y1, x2, y2, ...]
 * @returns True if any line segments intersect with each other
 */
export const hasSelfintersection = (points: number[]): boolean => {
  if (points.length < 8) {
    // Need at least 4 points (8 coordinates) to have 2 line segments
    return false;
  }

  // Convert points array to line segments
  const segments: LineSegment[] = [];
  for (let i = 0; i < points.length - 2; i += 2) {
    segments.push({
      x1: points[i],
      y1: points[i + 1],
      x2: points[i + 2],
      y2: points[i + 3],
    });
  }

  // Check each segment against all other segments
  for (let i = 0; i < segments.length; i++) {
    for (let j = i + 1; j < segments.length; j++) {
      // Skip adjacent segments (they naturally connect at endpoints)
      // Also skip the very next segment to avoid false positives from shared endpoints
      if (j === i + 1) {
        continue;
      }

      if (doLinesIntersect(segments[i], segments[j])) {
        return true;
      }
    }
  }

  return false;
};

/**
 * Checks if adding a new point would create an intersection
 * More efficient version that only checks the new segment against existing ones
 * @param points - Existing points array [x1, y1, x2, y2, ...]
 * @param newX - X coordinate of the new point
 * @param newY - Y coordinate of the new point
 * @returns True if adding the new point would create an intersection
 */
export const wouldCreateIntersection = (
  points: number[],
  newX: number,
  newY: number,
): boolean => {
  if (points.length < 4) {
    // Need at least 2 existing points to form a segment
    return false;
  }

  // Create the new segment from the last point to the new point
  const lastPointIndex = points.length - 2;
  const newSegment: LineSegment = {
    x1: points[lastPointIndex],
    y1: points[lastPointIndex + 1],
    x2: newX,
    y2: newY,
  };

  // Check the new segment against all existing segments (except the immediately previous one)
  for (let i = 0; i < points.length - 4; i += 2) {
    const existingSegment: LineSegment = {
      x1: points[i],
      y1: points[i + 1],
      x2: points[i + 2],
      y2: points[i + 3],
    };

    if (doLinesIntersect(newSegment, existingSegment)) {
      return true;
    }
  }

  return false;
};
