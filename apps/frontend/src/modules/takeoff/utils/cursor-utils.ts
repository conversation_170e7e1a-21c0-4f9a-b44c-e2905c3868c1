import { SelectedTool } from '../types/drawing-types';

/**
 * Utility functions for cursor management
 */

/**
 * Get the appropriate cursor icon name based on the selected tool
 * Following updated cursor icon conventions
 */
export function getCursorIconName(tool: SelectedTool): string {
  switch (tool) {
    case 'pan':
      return 'Hand';
    case 'select':
      return 'MousePointer';
    case 'rectangle':
    case 'circle':
    case 'ellipse':
      return 'Plus';
    case 'point-to-point':
    case 'curve':
      return 'PenTool';
    case 'freehand':
      return 'Pencil';
    default:
      return 'MousePointer';
  }
}

/**
 * Get cursor description for accessibility
 */
export function getCursorDescription(
  tool: SelectedTool,
  isHovering: boolean = false,
): string {
  const baseDescriptions: Record<SelectedTool, string> = {
    pan: 'Pan tool - drag to move the canvas',
    select: isHovering
      ? 'Move selected items'
      : 'Select tool - click to select items',
    rectangle: 'Rectangle tool - drag to draw rectangles',
    circle: 'Circle tool - drag to draw circles',
    ellipse: 'Ellipse tool - drag to draw ellipses',
    freehand: 'Freehand tool - drag to draw freehand lines',
    curve: 'Curve tool - click to add anchor points',
    'point-to-point': 'Point-to-point tool - click to add points',
    point: 'Point tool - click to place point markers',
    comment: 'Comment tool - click to add comment annotations',
    arrow: 'Arrow tool - click to draw arrows',
  };

  return baseDescriptions[tool] || 'Drawing tool';
}

/**
 * Check if a tool should show crosshair overlay when hovering over drawings
 */
export function shouldShowCrosshair(tool: SelectedTool): boolean {
  return [
    'rectangle',
    'circle',
    'ellipse',
    'freehand',
    'curve',
    'point-to-point',
    'point',
  ].includes(tool);
}

/**
 * Get cursor color class based on tool state
 */
export function getCursorColorClass(
  isDrawing: boolean,
  isDragging: boolean,
): string {
  if (isDrawing || isDragging) {
    return 'text-blue-600';
  }
  return 'text-gray-700';
}
