/**
 * Drawing constants for the takeoff module
 */

// Point shape sizes
export const POINT_SIZES = {
  // Base size for all point shapes (used as radius for circle, half-width for square, circumradius for triangle)
  DEFAULT: 8,
  // Adjusted sizes to make circle and square visually match triangle
  CIRCLE: 7, // Slightly smaller than default
  SQUARE: 6.5, // Smaller to match triangle visual size
  TRIANGLE: 8, // Keep original size as reference
} as const;

// Stroke widths
export const STROKE_WIDTHS = {
  THIN: 1,
  NORMAL: 1.5,
  THICK: 2,
  MEASURE_LINE: 1,
} as const;

// Arrow pointer dimensions
export const ARROW_POINTER = {
  LENGTH: 10,
  WIDTH: 10,
} as const;

// Default colors
export const DRAWING_COLORS = {
  // Stroke colors
  DEFAULT_STROKE: 'black',
  MEASURE_STROKE: '#3b82f6',

  // Fill colors
  TRANSPARENT_FILL: 'transparent',
  MEASURE_FILL: '#00000080', // Semi-transparent black for measure curves
  MEASURE_SURFACE_FILL: '#3b82f680', // Default measure color with transparency
} as const;

// Drawing behavior constants
export const DRAWING_BEHAVIOR = {
  CIRCLE_APPROXIMATION_SIDES: 8, // Use 8 sides for smooth circle approximation
} as const;
