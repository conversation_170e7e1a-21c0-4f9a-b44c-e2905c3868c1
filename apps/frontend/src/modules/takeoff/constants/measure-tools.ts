import {
  Circle as CircleIcon,
  Pen,
  PencilRuler,
  Spline,
  Square,
  Squircle,
  Waypoints,
} from 'lucide-react';
import { MeasureToolVariant } from '../store/takeoff-store';
import { SelectedTool } from '../types/drawing-types';
import { DRAWING_COLORS } from './drawing';

export interface MeasureToolOption {
  id: MeasureToolVariant;
  baseTool: SelectedTool;
  label: string;
  icon: React.ElementType;
  description: string;
  closed?: boolean;
  isSurface: boolean;
}

export const measureToolOptions: MeasureToolOption[] = [
  {
    id: 'measure-point-to-point-line',
    baseTool: 'point-to-point',
    label: 'Measure Line',
    icon: PencilRuler,
    description: 'Measure distance between points',
    closed: false,
    isSurface: false,
  },
  {
    id: 'measure-rectangle',
    baseTool: 'rectangle',
    label: 'Measure Rectangle',
    icon: Square,
    description: 'Measure rectangular areas',
    isSurface: true,
  },
  {
    id: 'measure-circle',
    baseTool: 'circle',
    label: 'Measure Circle',
    icon: CircleIcon,
    description: 'Measure circular areas',
    isSurface: true,
  },
  {
    id: 'measure-ellipse',
    baseTool: 'ellipse',
    label: 'Measure Ellipse',
    icon: Squircle,
    description: 'Measure elliptical areas',
    isSurface: true,
  },
  {
    id: 'measure-freehand-line',
    baseTool: 'freehand',
    label: 'Measure Freehand Line',
    icon: Pen,
    description: 'Measure freehand line lengths',
    closed: false,
    isSurface: false,
  },
  {
    id: 'measure-freehand-area',
    baseTool: 'freehand',
    label: 'Measure Freehand Area',
    icon: Pen,
    description: 'Measure freehand enclosed areas',
    closed: true,
    isSurface: true,
  },
  {
    id: 'measure-point-to-point-area',
    baseTool: 'point-to-point',
    label: 'Measure Polygon',
    icon: Waypoints,
    description: 'Measure polygon areas by connecting points',
    closed: true,
    isSurface: true,
  },
  {
    id: 'measure-curve',
    baseTool: 'curve',
    label: 'Measure Curve',
    icon: Spline,
    description: 'Measure curved line lengths',
    isSurface: false,
  },
];

export const getMeasureToolProperties = (measureTool: MeasureToolVariant) => {
  const toolOption = measureToolOptions.find((opt) => opt.id === measureTool);
  if (!toolOption)
    return { closed: false, isSurface: false, baseTool: 'point-to-point' };

  return {
    closed: toolOption.closed || false,
    isSurface: toolOption.isSurface,
    baseTool: toolOption.baseTool,
    fillColor: toolOption.isSurface
      ? DRAWING_COLORS.MEASURE_SURFACE_FILL
      : DRAWING_COLORS.TRANSPARENT_FILL,
  };
};
