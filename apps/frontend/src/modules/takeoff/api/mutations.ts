import { apiClient } from '@/lib/api-client';
import { queryKeys } from '@/lib/query-keys'; // Import centralized query keys
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { v4 as uuidv4 } from 'uuid';
import {
  Component,
  CreateComponentPayload,
  UpdateComponentPayload,
} from '../types/component';
import {
  CreateDrawingPayload,
  Drawing,
  UpdateDrawingPayload,
  UpdateDrawingResponse,
} from '../types/drawing';
import { useTakeoffStore, type ImageType } from '../store/takeoff-store'; // For Scale type

// Hook for creating a new component
export const useCreateComponent = () => {
  const queryClient = useQueryClient();

  return useMutation<Component, Error, CreateComponentPayload>({
    mutationFn: async (payload) => {
      return apiClient.post('/components', payload);
    },
    onSuccess: (_data, variables) => {
      // variables is CreateComponentPayload
      // Invalidate all component queries associated with this blueprintFileId.
      // This is a broader invalidation that ensures all views (aggregate, specific page, any search/tab state)
      // for this file's components are refreshed.
      // The key ['takeoff/components', variables.blueprintFileId] will match any query
      // that starts with this prefix, regardless of subsequent elements like blueprintImageId, activeTab, or search.
      queryClient.invalidateQueries({
        queryKey: [
          queryKeys.takeoff.components(
            variables.blueprintFileId,
            undefined,
            undefined,
            undefined,
          )[0],
          variables.blueprintFileId,
        ],
      });
    },
  });
};

// Hook for updating an existing component
export const useUpdateComponent = () => {
  const queryClient = useQueryClient();
  const { selectedImage } = useTakeoffStore((state) => state);

  return useMutation<Component, Error, UpdateComponentPayload>({
    mutationFn: async (payload) => {
      const { id, ...updateData } = payload;
      return apiClient.put(`/components/${id}`, updateData);
    },
    onSuccess: (_data, variables) => {
      if (variables.blueprintFileId) {
        // Broader invalidation for components associated with this blueprintFileId
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.takeoff.components(
              variables.blueprintFileId,
              undefined,
              undefined,
              undefined,
            )[0],
            variables.blueprintFileId,
          ],
        });

        // If the component update could affect drawings (e.g., page-specific component)
        if (selectedImage?.id) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.takeoff.drawings(selectedImage.id),
          });
        }
      }
    },
  });
};

// Hook for deleting a component
interface DeleteComponentVariables {
  id: number;
  blueprintFileId: string;
  blueprintImageId: string; // Assuming this will always be present for deletion context
}

export const useDeleteComponent = () => {
  const queryClient = useQueryClient();

  return useMutation<void, Error, DeleteComponentVariables>({
    mutationFn: async ({ id }) => {
      return apiClient.delete(`/components/${id}/hard`);
    },
    onSuccess: (_data, variables) => {
      // variables is DeleteComponentVariables
      // Broader invalidation for components associated with this blueprintFileId
      queryClient.invalidateQueries({
        queryKey: [
          queryKeys.takeoff.components(
            variables.blueprintFileId,
            undefined,
            undefined,
            undefined,
          )[0],
          variables.blueprintFileId,
        ],
      });

      // Invalidate drawings for the specific image.
      // This is crucial as deleting a component often means its drawings are also deleted or affected.
      queryClient.invalidateQueries({
        queryKey: queryKeys.takeoff.drawings(variables.blueprintImageId),
      });
    },
  });
};

type CreateDrawingResponse = {
  message: string;
  data: Drawing;
};

// Using uuid for generating unique optimistic IDs
// This prevents React key collisions when multiple drawings are created simultaneously

// Context type for optimistic updates
interface OptimisticUpdateContext {
  previousDrawings?: Drawing[];
  optimisticDrawingTempId?: number;
}

// Context type for optimistic updates for updating a drawing
interface OptimisticUpdateDrawingContext {
  previousDrawings?: Drawing[];
  drawingIdToUpdate?: number;
  originalBlueprintImageId?: string;
}

// Hook for updating an existing drawing
export const useUpdateDrawing = () => {
  const queryClient = useQueryClient();

  return useMutation<
    UpdateDrawingResponse,
    Error,
    UpdateDrawingPayload,
    OptimisticUpdateDrawingContext
  >({
    mutationFn: async (
      payload: UpdateDrawingPayload,
    ): Promise<UpdateDrawingResponse> => {
      const {
        drawingId,
        componentId,
        config,
        skipOptimisticUpdate: _skipOptimisticUpdate,
      } = payload;
      // The actual UpdateDrawingPayload should include blueprintImageId for cache handling,
      // but it's not used in the API call itself.
      const apiPayload: {
        componentId?: number;
        config: Record<string, string>;
      } = { config };
      if (componentId !== undefined) {
        apiPayload.componentId = componentId; // componentId is already a number from UpdateDrawingPayload
      }
      return apiClient.patch(`/drawings/${drawingId}`, apiPayload);
    },
    onMutate: async (updateVariables: UpdateDrawingPayload) => {
      // Skip optimistic updates if flag is set (for undo/redo operations)
      if (updateVariables.skipOptimisticUpdate) {
        return {
          previousDrawings: undefined,
          drawingIdToUpdate: undefined,
          originalBlueprintImageId: undefined,
        };
      }

      // Ensure blueprintImageId is part of updateVariables for correct cache handling
      if (!updateVariables.blueprintImageId) {
        console.error(
          'blueprintImageId is required for optimistic update of drawing',
        );
        // Potentially throw an error or return to prevent mutation without it
        return {
          previousDrawings: undefined,
          drawingIdToUpdate: undefined,
          originalBlueprintImageId: undefined,
        };
      }

      const queryKey = queryKeys.takeoff.drawings(
        updateVariables.blueprintImageId,
      );
      await queryClient.cancelQueries({ queryKey });

      const previousDrawings = queryClient.getQueryData<Drawing[]>(queryKey);

      if (previousDrawings) {
        queryClient.setQueryData<Drawing[]>(
          queryKey,
          (oldDrawings) =>
            oldDrawings?.map((drawing) =>
              drawing.id === updateVariables.drawingId
                ? {
                    ...drawing,
                    config: { ...drawing.config, ...updateVariables.config }, // Merge new config
                    componentId:
                      updateVariables.componentId !== undefined
                        ? updateVariables.componentId
                        : drawing.componentId,
                    updatedAt: new Date().toISOString(), // Optimistically update timestamp
                  }
                : drawing,
            ) || [],
        );
      }

      return {
        previousDrawings,
        drawingIdToUpdate: updateVariables.drawingId,
        originalBlueprintImageId: updateVariables.blueprintImageId,
      };
    },
    onError: (err, _updateVariables, context) => {
      console.error('Error updating drawing:', err);
      if (context?.previousDrawings && context.originalBlueprintImageId) {
        queryClient.setQueryData<Drawing[]>(
          queryKeys.takeoff.drawings(context.originalBlueprintImageId),
          context.previousDrawings,
        );
      }
    },
    onSuccess: (serverResponse, updateVariables, context) => {
      // Ensure blueprintImageId is available from context or variables for correct cache handling
      const blueprintImageId =
        context?.originalBlueprintImageId || updateVariables.blueprintImageId;
      if (!blueprintImageId) {
        console.error(
          'blueprintImageId is missing in onSuccess for useUpdateDrawing, cannot update cache accurately.',
        );
        return;
      }

      const actualUpdatedDrawing = serverResponse.data;
      queryClient.setQueryData<Drawing[]>(
        queryKeys.takeoff.drawings(blueprintImageId),
        (oldDrawings) =>
          oldDrawings?.map((drawing) =>
            drawing.id === actualUpdatedDrawing.id
              ? actualUpdatedDrawing
              : drawing,
          ) || [actualUpdatedDrawing], // Fallback if oldDrawings is undefined
      );
    },
  });
};

// Context type for optimistic updates for deleting a drawing
interface OptimisticDeleteDrawingContext {
  previousDrawings?: Drawing[];
  deletedDrawingId?: number;
  originalBlueprintImageId?: string;
}

// Hook for deleting a drawing
interface DeleteDrawingVariables {
  drawingId: number;
  blueprintImageId: string;
  // Optional flag to skip optimistic updates (for undo/redo operations)
  skipOptimisticUpdate?: boolean;
}

export const useDeleteDrawing = () => {
  const queryClient = useQueryClient();

  return useMutation<
    void,
    Error,
    DeleteDrawingVariables,
    OptimisticDeleteDrawingContext
  >({
    mutationFn: async ({ drawingId }) => {
      return apiClient.delete(`/drawings/${drawingId}/hard`);
    },
    onMutate: async (deleteVariables: DeleteDrawingVariables) => {
      // Skip optimistic updates if flag is set (for undo/redo operations)
      if (deleteVariables.skipOptimisticUpdate) {
        return {
          previousDrawings: undefined,
          deletedDrawingId: undefined,
          originalBlueprintImageId: undefined,
        };
      }

      const queryKey = queryKeys.takeoff.drawings(
        deleteVariables.blueprintImageId,
      );
      await queryClient.cancelQueries({ queryKey });

      const previousDrawings = queryClient.getQueryData<Drawing[]>(queryKey);

      if (previousDrawings) {
        // Optimistically remove the drawing from cache
        queryClient.setQueryData<Drawing[]>(
          queryKey,
          (oldDrawings) =>
            oldDrawings?.filter(
              (drawing) => drawing.id !== deleteVariables.drawingId,
            ) || [],
        );
      }

      return {
        previousDrawings,
        deletedDrawingId: deleteVariables.drawingId,
        originalBlueprintImageId: deleteVariables.blueprintImageId,
      };
    },
    onError: (err, _deleteVariables, context) => {
      console.error('Error deleting drawing:', err);
      if (context?.previousDrawings && context.originalBlueprintImageId) {
        // Restore the previous state if deletion failed
        queryClient.setQueryData<Drawing[]>(
          queryKeys.takeoff.drawings(context.originalBlueprintImageId),
          context.previousDrawings,
        );
      }
    },
    onSuccess: (_data, variables) => {
      // Final invalidation to ensure data consistency
      queryClient.invalidateQueries({
        queryKey: queryKeys.takeoff.drawings(variables.blueprintImageId),
      });
    },
  });
};

export const useCreateDrawing = () => {
  const queryClient = useQueryClient();

  return useMutation<
    CreateDrawingResponse,
    Error,
    CreateDrawingPayload,
    OptimisticUpdateContext
  >({
    mutationFn: async (
      payload: CreateDrawingPayload,
    ): Promise<CreateDrawingResponse> => {
      // Remove the optimistic component data and skipOptimisticUpdate flag before sending to API
      const {
        _optimisticComponent,
        skipOptimisticUpdate: _skipOptimisticUpdate,
        ...apiPayload
      } = payload;
      return apiClient.post('/drawings', apiPayload);
    },
    onMutate: async (newDrawingVariables) => {
      // Skip optimistic updates if flag is set (for undo/redo operations)
      if (newDrawingVariables.skipOptimisticUpdate) {
        return {
          previousDrawings: undefined,
          optimisticDrawingTempId: undefined,
        };
      }

      await queryClient.cancelQueries({
        queryKey: queryKeys.takeoff.drawings(
          newDrawingVariables.blueprintImageId,
        ),
      });

      const previousDrawings = queryClient.getQueryData<Drawing[]>(
        queryKeys.takeoff.drawings(newDrawingVariables.blueprintImageId),
      );

      // Generate unique optimistic ID using uuid to avoid collisions in concurrent operations
      // Convert uuid to a unique negative number for optimistic updates
      const uuid = uuidv4();
      const hash = uuid.split('-').join('').substring(0, 10); // Take first 10 chars after removing dashes
      const optimisticDrawingTempId = -parseInt(hash, 16); // Convert hex to negative number
      const optimisticDrawing: Drawing = {
        id: optimisticDrawingTempId,
        blueprintImageId: newDrawingVariables.blueprintImageId,
        componentId: newDrawingVariables.componentId || null,
        config: newDrawingVariables.config,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        deletedAt: null,
        // Include component data for optimistic update if available
        component: newDrawingVariables._optimisticComponent || null,
      };

      queryClient.setQueryData<Drawing[]>(
        queryKeys.takeoff.drawings(newDrawingVariables.blueprintImageId),
        (old) => [...(old || []), optimisticDrawing],
      );

      return { previousDrawings, optimisticDrawingTempId };
    },
    onError: (err, newDrawingVariables, context) => {
      if (context?.previousDrawings) {
        queryClient.setQueryData<Drawing[]>(
          queryKeys.takeoff.drawings(newDrawingVariables.blueprintImageId),
          context.previousDrawings,
        );
      }
      console.error('Error creating drawing:', err);
    },
    onSuccess: (serverResponse, newDrawingVariables, context) => {
      const actualNewDrawing = serverResponse.data;
      queryClient.setQueryData<Drawing[]>(
        queryKeys.takeoff.drawings(newDrawingVariables.blueprintImageId),
        (oldDrawings) =>
          oldDrawings?.map((drawing) =>
            drawing.id === context?.optimisticDrawingTempId
              ? actualNewDrawing
              : drawing,
          ) || [actualNewDrawing],
      );
      // It's often good practice to invalidate after optimistic update success to ensure consistency
      // with any server-generated fields or side-effects not handled optimistically.
      queryClient.invalidateQueries({
        queryKey: queryKeys.takeoff.drawings(
          newDrawingVariables.blueprintImageId,
        ),
      });
    },
    onSettled: (_data, _error, newDrawingVariables) => {
      // Final invalidation to ensure data consistency, especially if optimistic updates
      // don't cover all server-side changes or if there were errors.
      queryClient.invalidateQueries({
        queryKey: queryKeys.takeoff.drawings(
          newDrawingVariables.blueprintImageId,
        ),
      });
    },
  });
};

// Hook for updating an image's scale and dimensions
type ScaleUpdatePayload = NonNullable<ImageType['scale']>;
type DimensionsUpdatePayload = NonNullable<ImageType['dimensions']>;

interface UpdateImageScaleAndDimensionsVariables {
  imageId: string;
  scale: ScaleUpdatePayload;
  dimensions: DimensionsUpdatePayload;
}

export const useUpdateImageScaleAndDimensions = () => {
  return useMutation<unknown, Error, UpdateImageScaleAndDimensionsVariables>({
    mutationFn: async ({
      imageId,
      scale,
      dimensions,
    }: UpdateImageScaleAndDimensionsVariables) => {
      const response = await apiClient.patch(
        `/blueprint-files/images/${imageId}`,
        {
          scale,
          dimensions,
        },
      );
      return response.data; // Assuming the API returns the updated image data or a success response
    },
    // onSuccess and onError can be handled by the component calling the mutation,
    // or specific cache invalidations can be added here if needed globally.
    // For this case, invalidation is handled in PageItem.tsx.
  });
};

// Keep the old hook for backward compatibility
export const useUpdateImageScale = () => {
  return useMutation<unknown, Error, UpdateImageScaleVariables>({
    mutationFn: async ({ imageId, scale }: UpdateImageScaleVariables) => {
      const response = await apiClient.patch(
        `/blueprint-files/images/${imageId}`,
        { scale },
      );
      return response.data;
    },
  });
};

interface UpdateImageScaleVariables {
  imageId: string;
  scale: ScaleUpdatePayload;
}
