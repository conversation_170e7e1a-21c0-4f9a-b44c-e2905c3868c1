import { useQuery, UseQueryOptions } from '@tanstack/react-query'; // Added UseQueryOptions
import { apiClient } from '@/lib/api-client';
import { queryKeys } from '@/lib/query-keys'; // Import centralized query keys
import { BlueprintFilesResponse } from '../types/blueprint-files'; // Assuming this is still used or can be removed if not
import { Component, GetComponentsParams } from '../types/component';
import { Drawing, GetDrawingsParams } from '../types/drawing'; // Added import

export const useBlueprintFiles = (takeoffId: string) => {
  return useQuery({
    enabled: !!takeoffId,
    queryKey: queryKeys.takeoff.blueprintFiles(takeoffId),
    queryFn: (): Promise<BlueprintFilesResponse> =>
      apiClient.get(`/blueprint-files/takeoff/${takeoffId}`),
  });
};

export const useGetComponents = (params: GetComponentsParams) => {
  const { blueprintFileId, blueprintImageId, activeTab, search } = params;
  return useQuery<Component[], Error>({
    queryKey: queryKeys.takeoff.components(
      blueprintFileId,
      blueprintImageId,
      activeTab,
      search,
    ),
    queryFn: async (): Promise<Component[]> => {
      // Construct query parameters for the API call
      const queryParams = new URLSearchParams();

      queryParams.append('blueprintFileId', blueprintFileId);

      if (blueprintImageId && activeTab === 'specific') {
        queryParams.append('blueprintImageId', blueprintImageId);
      }

      if (search) {
        queryParams.append('search', search);
      }

      // geometryType is for client-side filtering, not sent to this API endpoint as per plan.

      return apiClient.get(`/components?${queryParams.toString()}`);
    },
    enabled:
      activeTab === 'specific'
        ? !!blueprintFileId && !!blueprintImageId
        : !!blueprintFileId,
  });
};

export const useBlueprintImages = (bluePrintId: string | undefined) => {
  return useQuery({
    enabled: !!bluePrintId,
    queryKey: queryKeys.takeoff.blueprintImages(bluePrintId!), // Add non-null assertion as enabled flag ensures it's defined
    queryFn: (): Promise<BlueprintFilesResponse> =>
      apiClient.get(`/blueprint-files/${bluePrintId}/images`, {
        params: {
          perPage: 100,
        },
      }),
  });
};

// --- Drawing Queries ---

/**
 * React Query hook for fetching drawings.
 * @param params - Contains blueprintImageId.
 * @param options - Optional React Query options.
 * @returns The query result including data, isLoading, isError, etc.
 */
export const useGetDrawings = (
  params: GetDrawingsParams,
  options?: Partial<
    UseQueryOptions<
      Drawing[],
      Error,
      Drawing[],
      ReturnType<typeof queryKeys.takeoff.drawings>
    >
  >,
) => {
  return useQuery<
    Drawing[],
    Error,
    Drawing[],
    ReturnType<typeof queryKeys.takeoff.drawings>
  >({
    queryKey: queryKeys.takeoff.drawings(params.blueprintImageId),
    queryFn: async (): Promise<Drawing[]> => {
      const queryParams = new URLSearchParams();
      queryParams.append('blueprintImageId', params.blueprintImageId);
      return apiClient.get(`/drawings?${queryParams.toString()}`);
    },
    enabled: !!params.blueprintImageId, // Only run the query if blueprintImageId is available
    ...options, // Spread any additional options
  });
};
