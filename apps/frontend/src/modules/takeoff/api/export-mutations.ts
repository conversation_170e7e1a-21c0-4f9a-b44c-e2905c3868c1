import { useMutation } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import { toast } from '@/lib/toast';

interface ExportExcelPayload {
  takeoffId: string;
  measurementView: 'current-page' | 'aggregate';
  blueprintImageId?: string;
}

export const useExportToExcel = () => {
  return useMutation({
    mutationFn: async (payload: ExportExcelPayload) => {
      const response = await apiClient.post(
        `/export/takeoffs/${payload.takeoffId}/excel`,
        {
          measurementView: payload.measurementView,
          blueprintImageId: payload.blueprintImageId,
        },
        {
          responseType: 'blob',
          timeout: 30000, // 30 second timeout for large exports
        },
      );
      return response.data;
    },
    onSuccess: (blob, variables) => {
      // Create and trigger download
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `takeoff_${variables.measurementView}_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      URL.revokeObjectURL(url);

      toast.success('Excel file downloaded successfully');
    },
    onError: (error) => {
      console.error('Export error:', error);
      toast.error('Failed to export Excel file');
    },
  });
};
