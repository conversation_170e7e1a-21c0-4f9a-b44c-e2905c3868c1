import { useState, useMemo } from 'react';
import { Component } from '../types/component';
import { Drawing } from '../types/drawing';
import {
  ComponentSummaryData,
  generateComponentSummary,
  ScaleInfo,
  PaperSize,
} from '@repo/component-summary';

export function useComponentSummary(
  components: Component[],
  drawings: Drawing[],
  scale: ScaleInfo | null,
  paperSize: PaperSize,
) {
  const [expandedComponents, setExpandedComponents] = useState<Set<number>>(
    new Set(),
  );

  // Calculate summary data for all components using shared package
  const summaryData = useMemo(() => {
    if (!scale) {
      // Return empty summary map when no scale is available
      const summaryMap = new Map<number, ComponentSummaryData>();
      components.forEach((component) => {
        summaryMap.set(component.id, {
          componentId: component.id,
          componentName: component.name,
          componentType: component.geometryType,
          totalMetrics: {
            type: 'count',
            value: 0,
            formattedValue: '0',
            unit: '',
          },
          drawings: [],
          drawingSummaryItems: [],
          isExpanded: expandedComponents.has(component.id),
        });
      });
      return summaryMap;
    }

    // Use shared package function to generate component summary
    const calculatedSummary = generateComponentSummary(
      components,
      drawings,
      scale,
      paperSize,
    );

    // Add expansion state to summary data
    const summaryMap = new Map<number, ComponentSummaryData>();
    calculatedSummary.forEach((summaryData, componentId) => {
      summaryMap.set(componentId, {
        ...summaryData,
        isExpanded: expandedComponents.has(componentId),
      });
    });

    return summaryMap;
  }, [components, drawings, expandedComponents, scale, paperSize]);

  const toggleExpansion = (componentId: number) => {
    setExpandedComponents((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(componentId)) {
        newSet.delete(componentId);
      } else {
        newSet.add(componentId);
      }
      return newSet;
    });
  };

  return {
    summaryData,
    toggleExpansion,
    expandedComponents,
  };
}
