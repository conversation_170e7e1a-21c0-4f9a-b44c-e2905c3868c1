import { useState, useEffect, RefObject } from 'react';
import { CanvasDimensions, CanvasPosition } from '../types/drawing-types';

interface UseCanvasDimensionsProps {
  containerRef: RefObject<HTMLDivElement | null>;
  initialDimensions?: CanvasDimensions;
  onDimensionsChange?: (dimensions: CanvasDimensions) => void;
  useFixedDimensions?: boolean; // Flag to use fixed dimensions instead of responsive
}

interface UseCanvasDimensionsReturn {
  dimensions: CanvasDimensions;
  setDimensions: React.Dispatch<React.SetStateAction<CanvasDimensions>>;
  resetPosition: () => void;
  position: CanvasPosition;
  setPosition: React.Dispatch<React.SetStateAction<CanvasPosition>>;
}

/**
 * Hook to manage canvas dimensions and position
 */
export function useCanvasDimensions({
  containerRef,
  initialDimensions = { width: 800, height: 600 },
  onDimensionsChange,
  useFixedDimensions = false,
}: UseCanvasDimensionsProps): UseCanvasDimensionsReturn {
  const [dimensions, setDimensions] =
    useState<CanvasDimensions>(initialDimensions);
  const [position, setPosition] = useState<CanvasPosition>({ x: 0, y: 0 });

  // Reset position to center
  const resetPosition = () => {
    setPosition({ x: 0, y: 0 });
  };

  // Update dimensions when container size changes (only if not using fixed dimensions)
  useEffect(() => {
    // Skip dimension updates if using fixed dimensions
    if (useFixedDimensions) {
      return;
    }

    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();

        // Adjust dimensions based on available space
        const newDimensions = {
          width: width - 32, // Subtract padding
          height: height - 32,
        };

        setDimensions(newDimensions);

        // Call the callback if provided
        if (onDimensionsChange) {
          onDimensionsChange(newDimensions);
        }

        // DO NOT reset position when dimensions change
        // This was causing drawings to shift when viewport changes (e.g., opening dev tools)
        // Position should only be reset when explicitly requested by the user
      }
    };

    // Initial update
    updateDimensions();

    // Update on resize
    window.addEventListener('resize', updateDimensions);

    // Use MutationObserver to detect DOM changes that might indicate sidebar state changes
    const observer = new MutationObserver((mutations) => {
      // Check if mutations affect the layout (like sidebar visibility changes)
      const shouldUpdate = mutations.some(
        (mutation) =>
          mutation.type === 'attributes' || mutation.type === 'childList',
      );

      if (shouldUpdate) {
        // Small delay to let the DOM settle after transitions
        setTimeout(updateDimensions, 50);
      }
    });

    // Start observing the document body for changes
    observer.observe(document.body, {
      attributes: true,
      childList: true,
      subtree: true,
    });

    return () => {
      window.removeEventListener('resize', updateDimensions);
      observer.disconnect();
    };
  }, [containerRef, dimensions.width, onDimensionsChange, useFixedDimensions]);

  return { dimensions, setDimensions, resetPosition, position, setPosition };
}
