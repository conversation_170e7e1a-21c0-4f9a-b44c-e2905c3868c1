import { generateRandomHexColor, generateRandomShade } from '@/lib/color-utils';
import { toast } from '@/lib/toast';
import { Circle, Layers, Minus } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  useCreateComponent,
  useDeleteComponent,
  useUpdateComponent,
} from '../api/mutations';
import { useGetComponents, useGetDrawings } from '../api/queries';
import { useComponentSummary } from './useComponentSummary';
import { AddComponentFormData } from '../components/add-component-modal';
import {
  ComponentItemData,
  ComponentSectionData,
} from '../components/CollapsibleComponentSection';
import { useTakeoffStore } from '../store/takeoff-store';
import {
  Component as ComponentData,
  CreateComponentPayload,
  GeometryType,
  SelectionType,
} from '../types/component';

const INITIAL_COMPONENT_SECTIONS: ComponentSectionData[] = [
  {
    type: 'surface',
    title: 'Surface',
    items: [],
    isLoading: true,
    isOpen: true,
    icon: Layers,
  },
  {
    type: 'edge',
    title: 'Edge',
    items: [],
    isLoading: true,
    isOpen: true,
    icon: Minus,
  },
  {
    type: 'point',
    title: 'Point',
    items: [],
    isLoading: true,
    isOpen: true,
    icon: Circle,
  },
];

export function useComponentManagement() {
  const [activeTab, setActiveTab] = useState<'aggregate' | 'specific'>(
    'specific',
  );
  const {
    selectedImage,
    selectedFile,
    selectedComponentItem,
    setSelectedComponentItem,
    isEditMode,
    locatedComponentId,
    setLocatedComponentId,
    paperSize,
  } = useTakeoffStore((state) => state);
  const [searchTerm, setSearchTerm] = useState('');

  const [componentSections, setComponentSections] = useState<
    ComponentSectionData[]
  >([...INITIAL_COMPONENT_SECTIONS].map((s) => ({ ...s }))); // Ensure deep copy for initial state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingComponent, setEditingComponent] =
    useState<ComponentData | null>(null);
  const [currentComponentTypeForModal, setCurrentComponentTypeForModal] =
    useState<GeometryType | null>(null);
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState(false);
  const [componentIdToDelete, setComponentIdToDelete] = useState<number | null>(
    null,
  );
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false);

  const {
    data: fetchedComponents,
    isLoading: isLoadingComponents,
    isError: isErrorComponents,
    error: componentsError,
  } = useGetComponents({
    blueprintFileId: selectedFile?.id || '',
    blueprintImageId: selectedImage?.id,
    activeTab,
    search: searchTerm,
  });

  // Add drawings query for component summary
  const { data: drawings = [] } = useGetDrawings({
    blueprintImageId: selectedImage?.id || '',
  });

  // Get all components for summary calculation
  const allComponents = componentSections.flatMap((section) => section.items);

  // Add component summary hook
  const { summaryData, toggleExpansion: toggleComponentSummary } =
    useComponentSummary(
      allComponents,
      drawings,
      selectedImage?.scale || null,
      paperSize,
    );

  const createComponentMutation = useCreateComponent();
  const updateComponentMutation = useUpdateComponent();
  const deleteComponentMutation = useDeleteComponent();

  useEffect(() => {
    if (isLoadingComponents) {
      setComponentSections((prev) =>
        prev.map((s) => ({ ...s, isLoading: true, items: [] })),
      );
      return;
    }

    if (isErrorComponents && componentsError) {
      console.error('Error fetching components:', componentsError);
      toast.error(
        'Failed to load components: ' + (componentsError as Error).message,
      );
      setComponentSections((prev) =>
        prev.map((s) => ({ ...s, isLoading: false, items: [] })),
      );
      return;
    }

    if (fetchedComponents) {
      const surfaceItems = fetchedComponents.filter(
        (c) => c.geometryType === 'surface',
      );
      const edgeItems = fetchedComponents.filter(
        (c) => c.geometryType === 'edge',
      );
      const pointItems = fetchedComponents.filter(
        (c) => c.geometryType === 'point',
      );

      setComponentSections((prevSections) =>
        prevSections.map((section) => {
          if (section.type === 'surface') {
            return { ...section, items: surfaceItems, isLoading: false };
          }
          if (section.type === 'edge') {
            return { ...section, items: edgeItems, isLoading: false };
          }
          if (section.type === 'point') {
            return { ...section, items: pointItems, isLoading: false };
          }
          return section;
        }),
      );
    } else {
      setComponentSections((prev) =>
        prev.map((s) => ({ ...s, isLoading: false, items: [] })),
      );
    }
  }, [
    fetchedComponents,
    isLoadingComponents,
    isErrorComponents,
    componentsError,
  ]);

  const handleSelectComponent = (item: ComponentItemData | null) => {
    // Only allow component selection in edit mode
    if (!isEditMode && item !== null) {
      toast.error('Component selection requires edit mode');
      return;
    }
    setSelectedComponentItem(
      item === null || selectedComponentItem?.id === item.id ? null : item,
    );
  };

  const handleShowComponent = (itemId: number) => {
    console.log(`Show component: ${itemId}`);
    // Implement show/hide logic
  };

  const handleLocateComponent = (itemId: number) => {
    // Toggle logic: if the same component is clicked, turn off locate
    // If different component is clicked, switch to that component
    if (locatedComponentId === itemId) {
      setLocatedComponentId(null);
    } else {
      setLocatedComponentId(itemId);
    }
  };

  const openModalForEdit = (component: ComponentData) => {
    setEditingComponent(component);
    setCurrentComponentTypeForModal(component.geometryType);
    setIsModalOpen(true);
  };

  const openModalForCreate = (type: GeometryType) => {
    setEditingComponent(null);
    setCurrentComponentTypeForModal(type);
    setIsModalOpen(true);
  };

  const closeModalAndReset = () => {
    setIsColorPickerOpen(false);
    setIsModalOpen(false);
    setEditingComponent(null);
    setCurrentComponentTypeForModal(null);
  };

  const openDeleteDialog = (itemId: number) => {
    setComponentIdToDelete(itemId);
    setIsDeleteDialogVisible(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogVisible(false);
    setComponentIdToDelete(null);
  };

  const handleEditComponent = (itemId: number, isColorPicker?: boolean) => {
    if (!isEditMode) {
      toast.error('Component editing requires edit mode');
      return;
    }

    const componentToEdit = fetchedComponents?.find((c) => c.id === itemId);
    if (componentToEdit) {
      openModalForEdit(componentToEdit);
    } else {
      toast.error('Could not find component to edit.');
    }

    if (isColorPicker) {
      setIsColorPickerOpen(true);
    }
  };

  const handleDeleteComponent = (itemId: number) => {
    if (!isEditMode) {
      toast.error('Component deletion requires edit mode');
      return;
    }
    openDeleteDialog(itemId);
  };

  const confirmDeleteComponent = async () => {
    if (!componentIdToDelete) return;

    if (!selectedFile?.id || !selectedImage?.id) {
      toast.error(
        'Cannot delete component: Missing selected file or image context.',
      );
      closeDeleteDialog();
      return;
    }

    try {
      await deleteComponentMutation.mutateAsync({
        id: componentIdToDelete,
        blueprintFileId: selectedFile.id,
        blueprintImageId: selectedImage.id,
      });
      toast.success('Component deleted successfully!');
      if (selectedComponentItem?.id === componentIdToDelete) {
        setSelectedComponentItem(null);
      }
    } catch (error) {
      const errorMsg =
        (error as Error)?.message || 'An unknown error occurred.';
      toast.error(`Failed to delete component: ${errorMsg}`);
      console.error('Failed to delete component:', error);
    } finally {
      closeDeleteDialog();
    }
  };

  const handleToggleSectionOpen = (type: GeometryType) => {
    setComponentSections((prev) =>
      prev.map((s) => (s.type === type ? { ...s, isOpen: !s.isOpen } : s)),
    );
  };

  const handleAddComponent = (type: GeometryType) => {
    if (!isEditMode) {
      toast.error('Component creation requires edit mode');
      return;
    }
    openModalForCreate(type);
  };

  const handleSaveComponentFromModal = async (
    formData: AddComponentFormData,
    componentType: GeometryType,
    isEditing: boolean,
    originalComponentId?: number,
  ) => {
    if (!selectedFile?.id) {
      toast.error('A blueprint file must be selected.');
      return;
    }

    const selectionTypeUI =
      formData.commonToAllPages === 'yes' ? 'allPages' : 'currentPage';
    let currentBlueprintImageId: string | undefined = undefined;

    if (selectionTypeUI === 'currentPage') {
      if (!selectedImage?.id) {
        toast.error(
          'A page image must be selected for page-specific components.',
        );
        return;
      }
      currentBlueprintImageId = selectedImage.id;
    }

    const geometricData =
      componentType === 'point'
        ? {
            geometryType: componentType,
            pointType: formData.pointType || 'circle',
          }
        : { geometryType: componentType };

    const payload: CreateComponentPayload = {
      name: formData.componentName,
      selectionType: selectionTypeUI as SelectionType,
      blueprintFileId: selectedFile.id,
      geometryType: componentType,
      blueprintImageId: currentBlueprintImageId,
      description: formData.description || '',
      color: isEditing ? (formData.color ?? '') : generateRandomHexColor(),
      shade: generateRandomShade(),
      geometricData: geometricData,
    };

    try {
      if (isEditing && originalComponentId) {
        await updateComponentMutation.mutateAsync({
          ...payload,
          id: originalComponentId,
        });
        toast.success('Component updated successfully!');
      } else {
        await createComponentMutation.mutateAsync(payload);
        toast.success('Component created successfully!');
      }
      closeModalAndReset();
    } catch (error) {
      const errorMsg =
        (error as Error)?.message || 'An unknown error occurred.';
      toast.error(`Failed to save component: ${errorMsg}`);
      console.error('Failed to save component:', error);
    }
  };

  return {
    selectedComponentItem,
    componentSections,
    isModalOpen,
    editingComponent,
    currentComponentTypeForModal,
    isDeleteDialogVisible,
    deleteComponentMutationIsPending: deleteComponentMutation.isPending,
    handleSelectComponent,
    handleShowComponent,
    handleLocateComponent,
    handleEditComponent,
    handleDeleteComponent,
    confirmDeleteComponent,
    handleToggleSectionOpen,
    handleAddComponent,
    handleSaveComponentFromModal,
    closeModalAndReset,
    closeDeleteDialog,
    activeTab,
    setActiveTab,
    searchTerm,
    setSearchTerm,
    isColorPickerOpen,
    setIsColorPickerOpen,
    // Component summary functionality
    summaryData,
    toggleComponentSummary,
  };
}
