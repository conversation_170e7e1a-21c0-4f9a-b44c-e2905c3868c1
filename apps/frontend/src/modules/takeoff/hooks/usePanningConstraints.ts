import { useCallback, useRef } from 'react';
import { CanvasDimensions, CanvasPosition } from '../types/drawing-types';
import { animatePosition, positionsEqual } from '../utils/animation-utils';

interface UsePanningConstraintsProps {
  imageDimensions: CanvasDimensions;
  viewportDimensions: CanvasDimensions;
  scale: number;
  setPosition: React.Dispatch<React.SetStateAction<CanvasPosition>>;
}

interface PanningBoundaries {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
}

interface UsePanningConstraintsReturn {
  constrainPosition: (newPosition: CanvasPosition) => CanvasPosition;
  triggerBounceBack: () => void;
  isWithinBounds: (position: CanvasPosition) => boolean;
  getBoundaries: () => PanningBoundaries;
  getConstrainedBoundaries: () => PanningBoundaries;
}

/**
 * Hook to manage panning constraints with bounce-back effects
 */
export function usePanningConstraints({
  imageDimensions,
  viewportDimensions,
  scale,
  setPosition,
}: UsePanningConstraintsProps): UsePanningConstraintsReturn {
  const animationCancelRef = useRef<(() => void) | null>(null);

  /**
   * Calculate the allowed panning boundaries
   * These are the "hard" boundaries - user cannot pan beyond these during interaction
   */
  const getBoundaries = useCallback((): PanningBoundaries => {
    // Calculate 50% overpan allowance for much more freedom of movement
    const overpanX = imageDimensions.width * 0.5;
    const overpanY = imageDimensions.height * 0.5;

    // Calculate boundaries accounting for scale and viewport
    const scaledImageWidth = imageDimensions.width * scale;
    const scaledImageHeight = imageDimensions.height * scale;
    const scaledOverpanX = overpanX * scale;
    const scaledOverpanY = overpanY * scale;

    return {
      // Maximum right position (image left edge can be this far from viewport left)
      maxX: scaledOverpanX,
      // Minimum left position (image right edge can be this far from viewport right)
      minX: viewportDimensions.width - scaledImageWidth - scaledOverpanX,
      // Maximum down position (image top edge can be this far from viewport top)
      maxY: scaledOverpanY,
      // Minimum up position (image bottom edge can be this far from viewport bottom)
      minY: viewportDimensions.height - scaledImageHeight - scaledOverpanY,
    };
  }, [imageDimensions, viewportDimensions, scale]);

  /**
   * Calculate the constrained boundaries where the image should snap back to
   * These are the "soft" boundaries - where the image should return to after overpanning
   */
  const getConstrainedBoundaries = useCallback((): PanningBoundaries => {
    const scaledImageWidth = imageDimensions.width * scale;
    const scaledImageHeight = imageDimensions.height * scale;

    // Much more generous constraints - allow canvas to rest far outside viewport
    const generousOverpan = Math.min(
      scaledImageWidth * 0.4,
      scaledImageHeight * 0.4,
    );

    // Base boundaries with generous overpan
    const boundaries = {
      maxX: generousOverpan,
      minX: viewportDimensions.width - scaledImageWidth - generousOverpan,
      maxY: generousOverpan,
      minY: viewportDimensions.height - scaledImageHeight - generousOverpan,
    };

    // For smaller images, still allow them to move generously around the viewport
    if (scaledImageWidth < viewportDimensions.width) {
      const centerX = (viewportDimensions.width - scaledImageWidth) / 2;
      // Allow generous movement around center position
      boundaries.maxX = centerX + generousOverpan;
      boundaries.minX = centerX - generousOverpan;
    }

    if (scaledImageHeight < viewportDimensions.height) {
      const centerY = (viewportDimensions.height - scaledImageHeight) / 2;
      // Allow generous movement around center position
      boundaries.maxY = centerY + generousOverpan;
      boundaries.minY = centerY - generousOverpan;
    }

    return boundaries;
  }, [imageDimensions, viewportDimensions, scale]);

  /**
   * Check if a position is within the constrained boundaries
   */
  const isWithinBounds = useCallback(
    (pos: CanvasPosition): boolean => {
      const boundaries = getConstrainedBoundaries();
      return (
        pos.x >= boundaries.minX &&
        pos.x <= boundaries.maxX &&
        pos.y >= boundaries.minY &&
        pos.y <= boundaries.maxY
      );
    },
    [getConstrainedBoundaries],
  );

  /**
   * Constrain a position to the allowed boundaries (with overpan)
   */
  const constrainPosition = useCallback(
    (newPosition: CanvasPosition): CanvasPosition => {
      const boundaries = getBoundaries();

      return {
        x: Math.max(boundaries.minX, Math.min(boundaries.maxX, newPosition.x)),
        y: Math.max(boundaries.minY, Math.min(boundaries.maxY, newPosition.y)),
      };
    },
    [getBoundaries],
  );

  /**
   * Find the nearest valid position within constrained boundaries
   */
  const findNearestValidPosition = useCallback(
    (pos: CanvasPosition): CanvasPosition => {
      const boundaries = getConstrainedBoundaries();

      return {
        x: Math.max(boundaries.minX, Math.min(boundaries.maxX, pos.x)),
        y: Math.max(boundaries.minY, Math.min(boundaries.maxY, pos.y)),
      };
    },
    [getConstrainedBoundaries],
  );

  /**
   * Trigger bounce-back animation to nearest valid position
   */
  const triggerBounceBack = useCallback(() => {
    // Cancel any existing animation
    if (animationCancelRef.current) {
      animationCancelRef.current();
      animationCancelRef.current = null;
    }

    // Get current position and check if we need to bounce back
    setPosition((currentPosition) => {
      // Check if we need to bounce back
      if (isWithinBounds(currentPosition)) {
        return currentPosition; // Already within bounds, no need to bounce back
      }

      const targetPosition = findNearestValidPosition(currentPosition);

      // Only animate if there's a meaningful difference
      if (positionsEqual(currentPosition, targetPosition, 2)) {
        return currentPosition;
      }

      // Start bounce-back animation
      animationCancelRef.current = animatePosition(
        currentPosition,
        targetPosition,
        300, // 300ms duration as specified
        (animatedPosition) => {
          setPosition(animatedPosition);
        },
        () => {
          animationCancelRef.current = null;
        },
      );

      return currentPosition; // Don't change position here, animation will handle it
    });
  }, [isWithinBounds, findNearestValidPosition, setPosition]);

  return {
    constrainPosition,
    triggerBounceBack,
    isWithinBounds,
    getBoundaries,
    getConstrainedBoundaries,
  };
}
