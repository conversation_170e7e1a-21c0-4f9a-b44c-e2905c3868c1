import { useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  SelectedTool,
  CanvasDimensions,
  DrawingShape,
} from '../types/drawing-types';
import { useUndoRedo } from './useUndoRedo';
import { useTakeoffStore } from '../store/takeoff-store';
import { useDeleteDrawing, useCreateDrawing } from '../api/mutations';
import { toast } from '@/lib/toast';
import { queryKeys } from '@/lib/query-keys';
import { Drawing, CreateDrawingPayload } from '../types/drawing';
import { getVisibleTools } from '../utils/tool-visibility';
import { areAllDrawingsWithinBounds } from '../utils/canvas-utils';

interface UseToolKeyboardShortcutsProps {
  setSelectedTool: (tool: SelectedTool) => void;
  selectedTool?: SelectedTool;
  setIsSpacebarPanning?: (isPanning: boolean) => void;
  dimensions: CanvasDimensions;
  cancelCurrentDrawing?: () => void;
  currentDrawing?: DrawingShape | null; // To check if there's an active drawing
  mousePositionRef: React.RefObject<{ x: number; y: number } | null>;
}

/**
 * Hook to handle keyboard shortcuts for drawing tools and undo/redo
 */
export function useToolKeyboardShortcuts({
  setSelectedTool,
  selectedTool,
  setIsSpacebarPanning,
  dimensions,
  cancelCurrentDrawing,
  currentDrawing,
  mousePositionRef,
}: UseToolKeyboardShortcutsProps) {
  const { undo, redo, recordAction } = useUndoRedo();
  const {
    selectedDrawingId,
    selectedDrawingIds,
    selectedImage,
    clearSelection,
    isEditMode,
    selectedComponentItem,
    clipboardData,
    setClipboardData,
    setSelectedDrawingIds,
  } = useTakeoffStore();
  const deleteDrawingMutation = useDeleteDrawing();
  const createDrawingMutation = useCreateDrawing();
  const queryClient = useQueryClient();

  const visibleTools = getVisibleTools(selectedComponentItem);
  const isToolVisible = useCallback(
    (tool: SelectedTool) => visibleTools.includes(tool),
    [visibleTools],
  );

  // Function to handle deleting selected drawings
  const handleDeleteSelected = useCallback(async () => {
    if (!selectedImage?.id) {
      toast.error('Cannot delete drawings: No image selected');
      return;
    }

    // Check if we have any selected drawings
    const hasMultipleSelected = selectedDrawingIds.length > 0;
    const hasSingleSelected = selectedDrawingId !== null;

    if (!hasMultipleSelected && !hasSingleSelected) {
      toast.error('No drawings selected');
      return;
    }

    try {
      if (hasMultipleSelected) {
        // Handle multiple selection deletion
        // First, we need to get the actual drawing objects
        // Since we only have IDs, we'll need to get them from the query cache
        const drawings =
          queryClient.getQueryData<Drawing[]>(
            queryKeys.takeoff.drawings(selectedImage.id),
          ) || [];
        const selectedDrawings = drawings.filter((drawing: Drawing) =>
          selectedDrawingIds.includes(drawing.id),
        );

        if (selectedDrawings.length === 0) {
          toast.error('Selected drawings not found');
          return;
        }

        // Delete all selected drawings
        const deletePromises = selectedDrawingIds.map((drawingId) =>
          deleteDrawingMutation.mutateAsync({
            drawingId,
            blueprintImageId: selectedImage.id,
          }),
        );

        await Promise.all(deletePromises);

        // Record the delete action for undo/redo
        recordAction({
          type: 'delete',
          data: {
            deletedDrawings: selectedDrawings,
          },
        });

        toast.success(`${selectedDrawingIds.length} drawing(s) deleted`);
        clearSelection();
      } else if (hasSingleSelected) {
        // Handle single selection deletion
        // Get the drawing object from cache
        const drawings =
          queryClient.getQueryData<Drawing[]>(
            queryKeys.takeoff.drawings(selectedImage.id),
          ) || [];
        const selectedDrawing = drawings.find(
          (drawing: Drawing) => drawing.id === selectedDrawingId,
        );

        if (!selectedDrawing) {
          toast.error('Selected drawing not found');
          return;
        }

        await deleteDrawingMutation.mutateAsync({
          drawingId: selectedDrawingId,
          blueprintImageId: selectedImage.id,
        });

        // Record the delete action for undo/redo
        recordAction({
          type: 'delete',
          data: {
            deletedDrawings: [selectedDrawing],
          },
        });

        toast.success('Drawing deleted');
        clearSelection();
      }
    } catch (error) {
      toast.error(
        `Failed to delete drawings: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }, [
    selectedImage,
    selectedDrawingIds,
    selectedDrawingId,
    deleteDrawingMutation,
    queryClient,
    recordAction,
    clearSelection,
  ]);

  // Function to handle copying selected drawings
  const handleKeyboardCopy = useCallback(() => {
    if (!selectedImage?.id) {
      toast.error('Cannot copy: No image selected');
      return;
    }

    // Determine selection type
    const hasMultipleSelected = selectedDrawingIds.length > 0;
    const hasSingleSelected = selectedDrawingId !== null;

    if (!hasMultipleSelected && !hasSingleSelected) {
      toast.error('No drawings selected');
      return;
    }

    // Get drawings from cache
    const drawings =
      queryClient.getQueryData<Drawing[]>(
        queryKeys.takeoff.drawings(selectedImage.id),
      ) || [];

    let selectedDrawings: Drawing[];
    if (hasMultipleSelected) {
      selectedDrawings = drawings.filter((d) =>
        selectedDrawingIds.includes(d.id),
      );
    } else {
      const singleDrawing = drawings.find((d) => d.id === selectedDrawingId);
      selectedDrawings = singleDrawing ? [singleDrawing] : [];
    }

    if (selectedDrawings.length === 0) {
      toast.error('Selected drawings not found');
      return;
    }

    setClipboardData({
      drawings: selectedDrawings,
      operation: 'copy',
    });

    toast.success(`${selectedDrawings.length} drawing(s) copied`);
  }, [
    selectedImage,
    selectedDrawingIds,
    selectedDrawingId,
    queryClient,
    setClipboardData,
  ]);

  // Function to handle cutting selected drawings
  const handleKeyboardCut = useCallback(async () => {
    if (!selectedImage?.id) {
      toast.error('Cannot cut: No image selected');
      return;
    }

    // Determine selection type
    const hasMultipleSelected = selectedDrawingIds.length > 0;
    const hasSingleSelected = selectedDrawingId !== null;

    if (!hasMultipleSelected && !hasSingleSelected) {
      toast.error('No drawings selected');
      return;
    }

    // Get drawings from cache
    const drawings =
      queryClient.getQueryData<Drawing[]>(
        queryKeys.takeoff.drawings(selectedImage.id),
      ) || [];

    let selectedDrawings: Drawing[];
    if (hasMultipleSelected) {
      selectedDrawings = drawings.filter((d) =>
        selectedDrawingIds.includes(d.id),
      );
    } else {
      const singleDrawing = drawings.find((d) => d.id === selectedDrawingId);
      selectedDrawings = singleDrawing ? [singleDrawing] : [];
    }

    if (selectedDrawings.length === 0) {
      toast.error('Selected drawings not found');
      return;
    }

    // Set clipboard data first
    setClipboardData({
      drawings: selectedDrawings,
      operation: 'cut',
    });

    try {
      // Delete all selected drawings
      const deletePromises = selectedDrawings.map((drawing) =>
        deleteDrawingMutation.mutateAsync({
          drawingId: drawing.id,
          blueprintImageId: selectedImage.id,
        }),
      );

      await Promise.all(deletePromises);

      // Record the delete action for undo/redo
      recordAction({
        type: 'delete',
        data: {
          deletedDrawings: selectedDrawings,
        },
      });

      toast.success(`${selectedDrawings.length} drawing(s) cut to clipboard`);
      clearSelection();
    } catch (error) {
      toast.error(
        `Failed to cut drawings: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // Clear clipboard data if delete failed
      setClipboardData(null);
    }
  }, [
    selectedImage,
    selectedDrawingIds,
    selectedDrawingId,
    queryClient,
    setClipboardData,
    deleteDrawingMutation,
    recordAction,
    clearSelection,
  ]);

  // Function to handle pasting from clipboard
  const handleKeyboardPaste = useCallback(async () => {
    if (!clipboardData || clipboardData.drawings.length === 0) {
      toast.error('No drawings in clipboard');
      return;
    }

    if (!selectedImage?.id) {
      toast.error('Cannot paste: No image selected');
      return;
    }

    // Get mouse position or fallback to center
    const mousePos = mousePositionRef.current;

    // Use mouse position or fallback to center
    const pastePosition = mousePos || {
      x: dimensions.width / 2,
      y: dimensions.height / 2,
    };

    // Calculate the bounding box of all drawings to maintain relative positions
    const drawingsData = clipboardData.drawings.map((drawing) => ({
      x: parseFloat(drawing.config.x || '0'),
      y: parseFloat(drawing.config.y || '0'),
      width: drawing.config.width
        ? parseFloat(drawing.config.width)
        : undefined,
      height: drawing.config.height
        ? parseFloat(drawing.config.height)
        : undefined,
      radius: drawing.config.radius
        ? parseFloat(drawing.config.radius)
        : undefined,
      points: drawing.config.points
        ? JSON.parse(drawing.config.points)
        : undefined,
    }));

    // Find the top-left corner of the selection bounding box
    let minX = Infinity;
    let minY = Infinity;

    drawingsData.forEach((drawingData) => {
      if (drawingData.radius !== undefined) {
        // Circle drawing
        minX = Math.min(minX, drawingData.x - drawingData.radius);
        minY = Math.min(minY, drawingData.y - drawingData.radius);
      } else if (
        drawingData.width !== undefined &&
        drawingData.height !== undefined
      ) {
        // Rectangle drawing - handle negative dimensions
        const left = Math.min(drawingData.x, drawingData.x + drawingData.width);
        const top = Math.min(drawingData.y, drawingData.y + drawingData.height);
        minX = Math.min(minX, left);
        minY = Math.min(minY, top);
      } else if (
        drawingData.points !== undefined &&
        drawingData.points.length >= 2
      ) {
        // Freehand drawing - check all points
        for (let i = 0; i < drawingData.points.length; i += 2) {
          minX = Math.min(minX, drawingData.points[i]);
          minY = Math.min(minY, drawingData.points[i + 1]);
        }
      }
    });

    // Create drawing objects with new positions to check bounds
    const newDrawingsToCheck = clipboardData.drawings.map((drawing) => {
      const originalX = parseFloat(drawing.config.x || '0');
      const originalY = parseFloat(drawing.config.y || '0');

      // Calculate relative position from the top-left of the selection
      const relativeX = originalX - minX;
      const relativeY = originalY - minY;

      // Position relative to the paste position
      const newX = pastePosition.x + relativeX;
      const newY = pastePosition.y + relativeY;

      // Handle freehand drawings - update all points
      let newPoints: number[] | undefined = undefined;
      if (drawing.config.points) {
        const originalPoints = JSON.parse(drawing.config.points);
        const offsetX = newX - originalX;
        const offsetY = newY - originalY;

        newPoints = [];
        for (let i = 0; i < originalPoints.length; i += 2) {
          newPoints.push(originalPoints[i] + offsetX);
          newPoints.push(originalPoints[i + 1] + offsetY);
        }
      }

      const transformedDrawing = {
        x: newX,
        y: newY,
        width: drawing.config.width
          ? parseFloat(drawing.config.width)
          : undefined,
        height: drawing.config.height
          ? parseFloat(drawing.config.height)
          : undefined,
        radius: drawing.config.radius
          ? parseFloat(drawing.config.radius)
          : undefined,
        points: newPoints,
        type: drawing.config.type, // Include type for special handling
      };

      return transformedDrawing;
    });

    // Check if all drawings will be within bounds after pasting
    if (!areAllDrawingsWithinBounds(newDrawingsToCheck, dimensions)) {
      toast.error("Drawing can't go out of plan extent");
      return;
    }

    try {
      // Create multiple drawings maintaining their relative positions
      const createPromises = clipboardData.drawings.map((drawing, index) => {
        const newDrawing = newDrawingsToCheck[index];

        const newDrawingConfig: Record<string, string> = {
          ...drawing.config,
          x: String(newDrawing.x),
          y: String(newDrawing.y),
        };

        // For freehand drawings, update the points with the new positions
        if (newDrawing.points) {
          newDrawingConfig.points = JSON.stringify(newDrawing.points);
        }

        const payload: CreateDrawingPayload = {
          blueprintImageId: selectedImage.id,
          config: newDrawingConfig,
        };

        // Preserve the original component from the copied/cut drawing
        if (drawing.componentId) {
          payload.componentId = drawing.componentId;
          payload._optimisticComponent = drawing.component || null;
        }

        return createDrawingMutation.mutateAsync(payload);
      });

      const responses = await Promise.all(createPromises);
      const count = clipboardData.drawings.length;

      // Record the paste action for undo/redo
      if (clipboardData.operation === 'cut') {
        // Cut-paste operation - record as atomic move operation
        recordAction({
          type: 'cut-paste',
          data: {
            originalDrawings: clipboardData.drawings,
            newDrawings: responses.map((response) => response.data),
          },
        });
      } else {
        // Copy-paste operation - record as create action
        recordAction({
          type: 'create',
          data: {
            drawings: responses.map((response) => response.data),
          },
        });
      }

      toast.success(`${count} drawing(s) pasted successfully`);

      // Clear clipboard if it was a cut operation
      if (clipboardData.operation === 'cut') {
        setClipboardData(null);
      }
    } catch (error) {
      toast.error(
        `Failed to paste drawings: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }, [
    clipboardData,
    selectedImage,
    mousePositionRef,
    dimensions,
    setClipboardData,
    createDrawingMutation,
    recordAction,
  ]);

  // Function to handle selecting all drawings
  const handleSelectAll = useCallback(() => {
    if (!selectedImage?.id) {
      toast.error('Cannot select all: No image selected');
      return;
    }

    if (!isEditMode) {
      toast.error('Select all requires edit mode');
      return;
    }

    // Get all drawings from cache
    const drawings =
      queryClient.getQueryData<Drawing[]>(
        queryKeys.takeoff.drawings(selectedImage.id),
      ) || [];

    if (drawings.length === 0) {
      toast.error('No drawings to select');
      return;
    }

    // Select all drawing IDs
    const allDrawingIds = drawings.map((drawing) => drawing.id);
    setSelectedDrawingIds(allDrawingIds);

    toast.success(`${allDrawingIds.length} drawing(s) selected`);
  }, [selectedImage, isEditMode, queryClient, setSelectedDrawingIds]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when not typing in an input field
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        e.target instanceof HTMLSelectElement ||
        (e.target as HTMLElement)?.contentEditable === 'true'
      ) {
        return;
      }

      // Handle undo/redo shortcuts with modifier keys
      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'z':
            e.preventDefault();
            // Only allow undo/redo in edit mode
            if (!isEditMode) {
              toast.error('Undo/Redo requires edit mode');
              return;
            }
            if (e.shiftKey) {
              // Ctrl+Shift+Z or Cmd+Shift+Z for redo
              redo();
            } else {
              // Ctrl+Z or Cmd+Z for undo
              undo();
            }
            break;
          case 'y':
            // Ctrl+Y or Cmd+Y for redo (Windows style)
            e.preventDefault();
            // Only allow undo/redo in edit mode
            if (!isEditMode) {
              toast.error('Undo/Redo requires edit mode');
              return;
            }
            redo();
            break;
          case 'c':
            // Ctrl+C or Cmd+C for copy
            e.preventDefault();
            if (!isEditMode) {
              toast.error('Copy requires edit mode');
              return;
            }
            handleKeyboardCopy();
            break;
          case 'x':
            // Ctrl+X or Cmd+X for cut
            e.preventDefault();
            if (!isEditMode) {
              toast.error('Cut requires edit mode');
              return;
            }
            handleKeyboardCut();
            break;
          case 'v':
            // Ctrl+V or Cmd+V for paste
            e.preventDefault();
            if (!isEditMode) {
              toast.error('Paste requires edit mode');
              return;
            }
            handleKeyboardPaste();
            break;
          case 'a':
            // Ctrl+A or Cmd+A for select all
            e.preventDefault();
            if (!isEditMode) {
              toast.error('Select all requires edit mode');
              return;
            }
            if (selectedTool !== 'select') {
              toast.error('Select all only works in select mode');
              return;
            }
            handleSelectAll();
            break;
          default:
            break;
        }
        return;
      }

      // Don't handle tool shortcuts when other modifier keys are pressed
      if (e.altKey) {
        return;
      }

      // Handle delete shortcuts
      if (e.key === 'Delete' || e.key === 'Backspace') {
        e.preventDefault();
        // Only allow delete in edit mode
        if (!isEditMode) {
          toast.error('Delete requires edit mode');
          return;
        }
        handleDeleteSelected();
        return;
      }

      // Handle spacebar for temporary pan mode
      if (e.key === ' ' || e.code === 'Space') {
        e.preventDefault();
        // Activate spacebar panning for all tools
        if (setIsSpacebarPanning) {
          setIsSpacebarPanning(true);
        }
        return;
      }

      // Handle escape key to cancel current drawing
      if (e.key === 'Escape') {
        e.preventDefault();
        // Only cancel if there's an active drawing and cancel function is provided
        if (currentDrawing && cancelCurrentDrawing) {
          cancelCurrentDrawing();
        }
        return;
      }

      // Handle tool selection shortcuts
      switch (e.key.toLowerCase()) {
        case 'p':
          e.preventDefault();
          setSelectedTool('pan');
          break;
        case 'r':
          e.preventDefault();
          // Only allow drawing tools in edit mode
          if (!isEditMode) {
            toast.error('Drawing tools require edit mode');
            return;
          }
          // Check if rectangle tool is available for current component type
          if (!isToolVisible('rectangle')) {
            toast.error('Rectangle tool not available for this component type');
            return;
          }
          setSelectedTool('rectangle');
          break;
        case 'c':
          e.preventDefault();
          // Only allow drawing tools in edit mode
          if (!isEditMode) {
            toast.error('Drawing tools require edit mode');
            return;
          }
          // Check if circle tool is available for current component type
          if (!isToolVisible('circle')) {
            toast.error('Circle tool not available for this component type');
            return;
          }
          setSelectedTool('circle');
          break;
        case 'e':
          e.preventDefault();
          // Only allow drawing tools in edit mode
          if (!isEditMode) {
            toast.error('Drawing tools require edit mode');
            return;
          }
          // Check if ellipse tool is available for current component type
          if (!isToolVisible('ellipse')) {
            toast.error('Ellipse tool not available for this component type');
            return;
          }
          setSelectedTool('ellipse');
          break;
        case 'f':
          e.preventDefault();
          // Only allow drawing tools in edit mode
          if (!isEditMode) {
            toast.error('Drawing tools require edit mode');
            return;
          }
          // Check if freehand tool is available for current component type
          if (!isToolVisible('freehand')) {
            toast.error('Freehand tool not available for this component type');
            return;
          }
          setSelectedTool('freehand');
          break;
        case 'v':
          e.preventDefault();
          // Only allow drawing tools in edit mode
          if (!isEditMode) {
            toast.error('Drawing tools require edit mode');
            return;
          }
          // Check if curve tool is available for current component type
          if (!isToolVisible('curve')) {
            toast.error('Curve tool not available for this component type');
            return;
          }
          setSelectedTool('curve');
          break;
        case 't':
          e.preventDefault();
          // Only allow drawing tools in edit mode
          if (!isEditMode) {
            toast.error('Drawing tools require edit mode');
            return;
          }
          // Check if point-to-point tool is available for current component type
          if (!isToolVisible('point-to-point')) {
            toast.error(
              'Point-to-point tool not available for this component type',
            );
            return;
          }
          setSelectedTool('point-to-point');
          break;
        case 'a':
          e.preventDefault();
          // Only allow arrow tool in edit mode with no component selected
          if (!isEditMode || selectedComponentItem) {
            toast.error(
              'Arrow tool requires edit mode with no component selected',
            );
            return;
          }
          setSelectedTool('arrow');
          break;
        case 's':
          e.preventDefault();
          // Only allow select tool in edit mode
          if (!isEditMode) {
            toast.error('Select tool requires edit mode');
            return;
          }
          setSelectedTool('select');
          break;
        default:
          break;
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      // Only handle shortcuts when not typing in an input field
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        e.target instanceof HTMLSelectElement ||
        (e.target as HTMLElement)?.contentEditable === 'true'
      ) {
        return;
      }

      // Handle spacebar release to deactivate temporary pan mode
      if (e.key === ' ' || e.code === 'Space') {
        e.preventDefault();
        if (setIsSpacebarPanning) {
          setIsSpacebarPanning(false);
        }
        return;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [
    setSelectedTool,
    selectedTool,
    setIsSpacebarPanning,
    undo,
    redo,
    handleDeleteSelected,
    isEditMode,
    selectedComponentItem,
    isToolVisible,
    handleKeyboardCopy,
    handleKeyboardCut,
    handleKeyboardPaste,
    handleSelectAll,
    cancelCurrentDrawing,
    currentDrawing,
  ]);
}
