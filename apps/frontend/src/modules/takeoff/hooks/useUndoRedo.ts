import { useCallback } from 'react';
import { useTakeoffStore } from '../store/takeoff-store';
import {
  useCreateDrawing,
  useUpdateDrawing,
  useDeleteDrawing,
} from '../api/mutations';
import { UndoRedoAction } from '../types/drawing-types';
import { toast } from '@/lib/toast';
import { v4 as uuidv4 } from 'uuid';

/**
 * Hook to manage undo/redo operations for drawing actions
 */
export function useUndoRedo() {
  const {
    undoRedoHistory,
    currentHistoryStep,
    addToHistory,
    updateDrawingInHistory,
    updateDrawingIdAcrossHistory,
    moveHistoryStep,
    clearHistory,
    selectedImage,
  } = useTakeoffStore();

  // Compute canUndo and canRedo locally to ensure reactivity
  const canUndo = currentHistoryStep >= 0;
  const canRedo = currentHistoryStep < undoRedoHistory.length - 1;

  const createDrawingMutation = useCreateDrawing();
  const updateDrawingMutation = useUpdateDrawing();
  const deleteDrawingMutation = useDeleteDrawing();

  /**
   * Helper function to handle 404 errors consistently
   */
  const handle404Error = useCallback(
    (error: any, drawingId: number | string, operation: string): boolean => {
      if (error?.response?.status === 404 || error?.statusCode === 404) {
        console.warn(
          `Drawing ${drawingId} not found during ${operation}, skipping operation`,
        );
        return true; // Indicates this is a 404 error that should be ignored
      }
      return false; // Not a 404 error, should be handled differently
    },
    [],
  );

  /**
   * Helper function to execute mutation with error handling
   */
  const executeMutationWithErrorHandling = useCallback(
    async <T>(
      mutationFn: () => Promise<T>,
      drawingId: number | string,
      operation: string,
      shouldThrow404 = false,
    ): Promise<T | null> => {
      try {
        return await mutationFn();
      } catch (error: any) {
        if (handle404Error(error, drawingId, operation)) {
          return null; // Continue with other operations
        }
        if (shouldThrow404) {
          throw error; // Re-throw other errors
        }
        console.error(`Failed to ${operation} drawing ${drawingId}:`, error);
        return null; // Continue with other operations
      }
    },
    [handle404Error],
  );

  /**
   * Helper function to execute batch operations with Promise.all
   */
  const executeBatchOperations = useCallback(
    async <T>(
      operations: Array<() => Promise<T | null>>,
    ): Promise<Array<T | null>> => {
      const promises = operations.map((op) => op());
      return Promise.all(promises);
    },
    [],
  );

  /**
   * Helper function to update drawing ID in history after creation
   */
  const updateDrawingIdInHistory = useCallback(
    (
      actionId: string,
      oldDrawingId: number,
      newDrawing: any,
      updateAcrossHistory = false,
    ) => {
      updateDrawingInHistory(actionId, oldDrawingId, newDrawing);
      if (updateAcrossHistory) {
        updateDrawingIdAcrossHistory(oldDrawingId, newDrawing);
      }
    },
    [updateDrawingInHistory, updateDrawingIdAcrossHistory],
  );

  /**
   * Helper function to create a drawing with history update
   */
  const createDrawingWithHistoryUpdate = useCallback(
    async (drawing: any, actionId: string, updateAcrossHistory = false) => {
      if (!selectedImage?.id) return null;

      const response = await createDrawingMutation.mutateAsync({
        blueprintImageId: selectedImage.id,
        config: drawing.config,
        componentId: drawing.componentId || undefined,
        _optimisticComponent: drawing.component || null,
        skipOptimisticUpdate: true,
      });

      const newDrawing = response.data;
      updateDrawingIdInHistory(
        actionId,
        drawing.id,
        newDrawing,
        updateAcrossHistory,
      );
      return response;
    },
    [selectedImage, createDrawingMutation, updateDrawingIdInHistory],
  );

  /**
   * Helper function to delete a drawing
   */
  const deleteDrawing = useCallback(
    async (drawingId: number, shouldThrow404 = false) => {
      if (!selectedImage?.id) return null;

      return executeMutationWithErrorHandling(
        () =>
          deleteDrawingMutation.mutateAsync({
            drawingId,
            blueprintImageId: selectedImage.id,
            skipOptimisticUpdate: true,
          }),
        drawingId,
        'delete',
        shouldThrow404,
      );
    },
    [selectedImage, deleteDrawingMutation, executeMutationWithErrorHandling],
  );

  /**
   * Helper function to update a drawing
   */
  const updateDrawing = useCallback(
    async (
      drawingId: number,
      config: any,
      componentId?: number,
      shouldThrow404 = false,
    ) => {
      if (!selectedImage?.id) return null;

      return executeMutationWithErrorHandling(
        () =>
          updateDrawingMutation.mutateAsync({
            drawingId,
            blueprintImageId: selectedImage.id,
            config,
            componentId: componentId || undefined,
            skipOptimisticUpdate: true,
          }),
        drawingId,
        'update',
        shouldThrow404,
      );
    },
    [selectedImage, updateDrawingMutation, executeMutationWithErrorHandling],
  );

  /**
   * Generic function to execute undo/redo operations
   */
  const executeOperation = useCallback(
    async (operation: 'undo' | 'redo', action: UndoRedoAction) => {
      try {
        if (action.type === 'create') {
          if (operation === 'undo') {
            // Undo create by deleting all the drawings
            const operations = (action.data as any).drawings.map(
              (drawing: any) => () => deleteDrawing(drawing.id, true),
            );
            await executeBatchOperations(operations);
          }
          if (operation === 'redo') {
            // Redo create by recreating all the drawings
            const operations = (action.data as any).drawings.map(
              (drawing: any) => () =>
                executeMutationWithErrorHandling(
                  () => createDrawingWithHistoryUpdate(drawing, action.id),
                  drawing.id,
                  'recreate during redo create',
                ),
            );
            await executeBatchOperations(operations);
          }
        }

        if (action.type === 'cut-paste') {
          if (operation === 'undo') {
            // Delete new drawings
            const deleteOperations = (action.data as any).newDrawings.map(
              (drawing: any) => () => deleteDrawing(drawing.id),
            );
            await executeBatchOperations(deleteOperations);

            // Recreate original drawings
            const recreateOperations = (
              action.data as any
            ).originalDrawings.map(
              (drawing: any) => () =>
                executeMutationWithErrorHandling(
                  () =>
                    createDrawingWithHistoryUpdate(drawing, action.id, true),
                  drawing.id,
                  'recreate original during undo cut-paste',
                ),
            );
            await executeBatchOperations(recreateOperations);
          }
          if (operation === 'redo') {
            // Delete original drawings
            const deleteOperations = (action.data as any).originalDrawings.map(
              (drawing: any) => () => deleteDrawing(drawing.id),
            );
            await executeBatchOperations(deleteOperations);

            // Recreate pasted drawings
            const recreateOperations = (action.data as any).newDrawings.map(
              (drawing: any) => () =>
                executeMutationWithErrorHandling(
                  () =>
                    createDrawingWithHistoryUpdate(drawing, action.id, true),
                  drawing.id,
                  'recreate pasted during redo cut-paste',
                ),
            );
            await executeBatchOperations(recreateOperations);
          }
        }

        if (action.type === 'delete') {
          if (operation === 'undo') {
            // Undo delete by recreating the drawing(s)
            const operations = (action.data as any).deletedDrawings.map(
              (drawing: any) => () =>
                executeMutationWithErrorHandling(
                  () => createDrawingWithHistoryUpdate(drawing, action.id),
                  drawing.id,
                  'recreate deleted during undo',
                ),
            );
            await executeBatchOperations(operations);
          }
          if (operation === 'redo') {
            // Redo delete by deleting the drawing(s) again
            const operations = (action.data as any).deletedDrawings.map(
              (drawing: any) => () => deleteDrawing(drawing.id, true),
            );
            await executeBatchOperations(operations);
          }
        }

        if (action.type === 'update' || action.type === 'move') {
          if (operation === 'undo') {
            // Undo update/move by restoring old data for all drawings
            const operations = (action.data as any).drawings.map(
              (drawingUpdate: any) => () =>
                updateDrawing(
                  drawingUpdate.id,
                  drawingUpdate.oldData.config,
                  drawingUpdate.oldData.componentId,
                  true,
                ),
            );
            await executeBatchOperations(operations);
          }
          if (operation === 'redo') {
            // Redo update/move by applying new data for all drawings
            const operations = (action.data as any).drawings.map(
              (drawingUpdate: any) => () =>
                updateDrawing(
                  drawingUpdate.id,
                  drawingUpdate.newData.config,
                  drawingUpdate.newData.componentId,
                  true,
                ),
            );
            await executeBatchOperations(operations);
          }
        }

        if (
          action.type !== 'create' &&
          action.type !== 'cut-paste' &&
          action.type !== 'delete' &&
          action.type !== 'update' &&
          action.type !== 'move'
        ) {
          // This case should never be reached due to exhaustive type checking
          toast.error('Unknown action type');
          return;
        }

        moveHistoryStep(operation);
        toast.success(`Action ${operation === 'undo' ? 'undone' : 'redone'}`);
      } catch (error) {
        toast.error(
          `Failed to ${operation}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
      }
    },
    [
      deleteDrawing,
      executeBatchOperations,
      executeMutationWithErrorHandling,
      createDrawingWithHistoryUpdate,
      updateDrawing,
      moveHistoryStep,
    ],
  );

  /**
   * Add a new action to the history
   */
  const recordAction = useCallback(
    (action: Omit<UndoRedoAction, 'id' | 'timestamp'>) => {
      const fullAction: UndoRedoAction = {
        id: uuidv4(),
        timestamp: Date.now(),
        type: action.type,
        data: action.data,
      } as UndoRedoAction;
      addToHistory(fullAction);
    },
    [addToHistory],
  );

  /**
   * Undo the last action
   */
  const undo = useCallback(async () => {
    if (!canUndo || currentHistoryStep < 0) {
      toast.error('Nothing to undo');
      return;
    }

    const action = undoRedoHistory[currentHistoryStep];
    if (!action || !selectedImage?.id) {
      toast.error('Cannot undo: Invalid action or no image selected');
      return;
    }

    await executeOperation('undo', action);
  }, [
    canUndo,
    currentHistoryStep,
    undoRedoHistory,
    selectedImage,
    executeOperation,
  ]);

  /**
   * Redo the next action
   */
  const redo = useCallback(async () => {
    if (!canRedo || currentHistoryStep >= undoRedoHistory.length - 1) {
      toast.error('Nothing to redo');
      return;
    }

    const nextAction = undoRedoHistory[currentHistoryStep + 1];
    if (!nextAction || !selectedImage?.id) {
      toast.error('Cannot redo: Invalid action or no image selected');
      return;
    }

    await executeOperation('redo', nextAction);
  }, [
    canRedo,
    currentHistoryStep,
    undoRedoHistory,
    selectedImage,
    executeOperation,
  ]);

  return {
    canUndo,
    canRedo,
    undo,
    redo,
    recordAction,
    clearHistory,
    historyLength: undoRedoHistory.length,
    currentStep: currentHistoryStep,
  };
}
