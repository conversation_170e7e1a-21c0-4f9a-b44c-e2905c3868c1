import { useState, useEffect, useCallback } from 'react';
import { SelectedTool } from '../types/drawing-types';
import { Drawing } from '../types/drawing';

interface CursorState {
  isHoveringDrawing: boolean;
  isDrawing: boolean;
  isDragging: boolean;
}

interface UseCustomCursorProps {
  selectedTool: SelectedTool;
  currentDrawing: any; // DrawingShape | null
  isShapeDragging: boolean;
  isGroupDragging: boolean;
  isSpacebarPanning: boolean;
  isMiddleMousePanning: boolean;
  _fetchedDrawings?: Drawing[];
  stageRef?: React.RefObject<any>; // Konva.Stage ref
}

export function useCustomCursor({
  selectedTool,
  currentDrawing,
  isShapeDragging,
  isGroupDragging,
  isSpacebarPanning,
  isMiddleMousePanning,
  _fetchedDrawings = [],
  stageRef,
}: UseCustomCursorProps) {
  const [cursorState, setCursorState] = useState<CursorState>({
    isHoveringDrawing: false,
    isDrawing: false,
    isDragging: false,
  });

  // Check if mouse is hovering over a drawing
  const checkHoverState = useCallback(
    (_mouseX: number, _mouseY: number) => {
      if (!stageRef?.current) return false;

      try {
        const stage = stageRef.current;
        const pointerPosition = stage.getPointerPosition();

        if (!pointerPosition) return false;

        // Get the shape under the pointer
        const shape = stage.getIntersection(pointerPosition);

        // Check if the shape belongs to a drawing (not selection box or other UI elements)
        if (shape && shape.name) {
          const shapeName = String(shape.name); // Convert to string safely
          if (
            shapeName.startsWith('drawing-') ||
            shapeName.includes('drawing')
          ) {
            return true;
          }
        }

        // Alternative check: look for shapes with drawing-related attributes
        if (shape && (shape.attrs?.drawingId || shape.attrs?.isDrawing)) {
          return true;
        }

        return false;
      } catch {
        // Silently handle errors to avoid console spam during normal operation
        return false;
      }
    },
    [stageRef],
  );

  // Update cursor state based on various conditions
  useEffect(() => {
    setCursorState((prev) => ({
      ...prev,
      isDrawing: !!currentDrawing && currentDrawing.type !== 'selection',
      isDragging: isShapeDragging || isGroupDragging,
    }));
  }, [currentDrawing, isShapeDragging, isGroupDragging]);

  // Mouse move handler for hover detection
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (selectedTool === 'select' || selectedTool === 'pan') {
        const isHovering = checkHoverState(e.clientX, e.clientY);
        setCursorState((prev) => ({
          ...prev,
          isHoveringDrawing: isHovering,
        }));
      } else {
        // For drawing tools, we might want to show crosshair when over existing drawings
        const isHovering = checkHoverState(e.clientX, e.clientY);
        setCursorState((prev) => ({
          ...prev,
          isHoveringDrawing: isHovering,
        }));
      }
    },
    [selectedTool, checkHoverState],
  );

  // Set up mouse move listener
  useEffect(() => {
    let animationFrame: number;

    const throttledMouseMove = (e: MouseEvent) => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }

      animationFrame = requestAnimationFrame(() => {
        handleMouseMove(e);
      });
    };

    window.addEventListener('mousemove', throttledMouseMove);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
      window.removeEventListener('mousemove', throttledMouseMove);
    };
  }, [handleMouseMove]);

  return {
    ...cursorState,
    isSpacebarPanning,
    isMiddleMousePanning,
    selectedTool,
  };
}
