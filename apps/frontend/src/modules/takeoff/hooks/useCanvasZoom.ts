import { useState, useCallback, useEffect, RefObject, useRef } from 'react';
import { CanvasPosition, CanvasDimensions } from '../types/drawing-types';
import { usePanningConstraints } from './usePanningConstraints';

interface UseCanvasZoomProps {
  minZoom?: number;
  initialZoom?: number;
  onZoomChange?: (scale: number) => void;
  setPosition: React.Dispatch<React.SetStateAction<CanvasPosition>>;
  containerRef?: RefObject<HTMLDivElement | null>; // Reference to the canvas container
  mousePositionRef?: RefObject<{ x: number; y: number } | null>; // Reference to track mouse position
  imageDimensions?: CanvasDimensions; // Image dimensions for panning constraints
  viewportDimensions?: CanvasDimensions; // Viewport dimensions for panning constraints
}

interface UseCanvasZoomReturn {
  zoom: number;
  setZoom: React.Dispatch<React.SetStateAction<number>>;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  handleWheel: (e: React.WheelEvent<HTMLDivElement>) => void;
  handleGestureChange: (e: React.TouchEvent<HTMLDivElement>) => void;
  resetTouchTracking: () => void;
  zoomPercentage: number;
  triggerBounceBack: () => void;
}

/**
 * Hook to manage canvas zoom functionality
 */
export function useCanvasZoom({
  minZoom = 0.8,
  initialZoom = 1,
  onZoomChange,
  setPosition,
  containerRef,
  mousePositionRef,
  imageDimensions,
  viewportDimensions,
}: UseCanvasZoomProps): UseCanvasZoomReturn {
  const [zoom, setZoom] = useState(initialZoom);

  // Use a ref to track the current zoom value for consistent calculations
  const zoomRef = useRef(zoom);

  // Keep the ref in sync with the state
  useEffect(() => {
    zoomRef.current = zoom;
  }, [zoom]);

  // Initialize panning constraints (always call hook, but only use if dimensions provided)
  const defaultDimensions: CanvasDimensions = { width: 1000, height: 1000 };
  const panningConstraints = usePanningConstraints({
    imageDimensions: imageDimensions || defaultDimensions,
    viewportDimensions: viewportDimensions || defaultDimensions,
    scale: zoom,
    setPosition,
  });

  // Helper function to get zoom center position in container coordinates
  const getZoomCenter = useCallback(
    (currentPosition: CanvasPosition): { x: number; y: number } => {
      // Convert mouse position from canvas coordinates to container coordinates
      if (mousePositionRef?.current) {
        // Transform canvas coordinates back to container coordinates
        const canvasPos = mousePositionRef.current;
        // Use zoom ref for consistent calculations
        const currentZoom = zoomRef.current;
        const containerX = canvasPos.x * currentZoom + currentPosition.x;
        const containerY = canvasPos.y * currentZoom + currentPosition.y;
        return { x: containerX, y: containerY };
      }

      // Fallback to canvas center
      if (containerRef?.current) {
        const rect = containerRef.current.getBoundingClientRect();
        return {
          x: rect.width / 2,
          y: rect.height / 2,
        };
      }

      // Final fallback
      return { x: 0, y: 0 };
    },
    [mousePositionRef, containerRef],
  );

  // Helper function to apply constraints to position updates
  const applyConstraints = useCallback(
    (newPosition: CanvasPosition): CanvasPosition => {
      if (imageDimensions && viewportDimensions) {
        return panningConstraints.constrainPosition(newPosition);
      }
      return newPosition;
    },
    [imageDimensions, viewportDimensions, panningConstraints],
  );

  // Helper function to trigger bounce-back if needed
  const triggerBounceBackIfNeeded = useCallback(() => {
    if (imageDimensions && viewportDimensions) {
      panningConstraints.triggerBounceBack();
    }
  }, [imageDimensions, viewportDimensions, panningConstraints]);

  // Calculate zoom percentage for display
  const zoomPercentage = Math.round(zoom * 100);

  // Zoom in function with pointer-centered zooming
  const zoomIn = useCallback(() => {
    // Get current zoom from ref for consistency
    const currentZoom = zoomRef.current;

    // Moderate increment for balanced zooming
    const increment = 0.1;
    const newZoom = currentZoom + increment;

    // Update zoom state first
    setZoom(newZoom);
    if (onZoomChange) onZoomChange(newZoom);

    // Update zoom ref immediately
    zoomRef.current = newZoom;

    // Get current position synchronously - same pattern as wheel zoom
    setPosition((prevPosition) => {
      const zoomCenter = getZoomCenter(prevPosition);

      // Calculate position change using same logic as wheel zoom
      const scaleFactor = newZoom / currentZoom;
      const dx = zoomCenter.x - (zoomCenter.x - prevPosition.x) * scaleFactor;
      const dy = zoomCenter.y - (zoomCenter.y - prevPosition.y) * scaleFactor;

      return applyConstraints({ x: dx, y: dy });
    });
  }, [onZoomChange, getZoomCenter, setPosition, applyConstraints]);

  // Zoom out function with pointer-centered zooming
  const zoomOut = useCallback(() => {
    // Get current zoom from ref for consistency
    const currentZoom = zoomRef.current;

    // Moderate decrement for balanced zooming
    const decrement = Math.min(0.1, (currentZoom - minZoom) * 0.15);
    const newZoom = Math.max(currentZoom - decrement, minZoom);

    // Update zoom state first
    setZoom(newZoom);
    if (onZoomChange) onZoomChange(newZoom);

    // Update zoom ref immediately
    zoomRef.current = newZoom;

    // Get current position synchronously - same pattern as wheel zoom
    setPosition((prevPosition) => {
      const zoomCenter = getZoomCenter(prevPosition);

      // Calculate position change using same logic as wheel zoom
      const scaleFactor = newZoom / currentZoom;
      const dx = zoomCenter.x - (zoomCenter.x - prevPosition.x) * scaleFactor;
      const dy = zoomCenter.y - (zoomCenter.y - prevPosition.y) * scaleFactor;

      return applyConstraints({ x: dx, y: dy });
    });
  }, [minZoom, onZoomChange, getZoomCenter, setPosition, applyConstraints]);

  // Reset zoom and position
  const resetZoom = useCallback(() => {
    setZoom(initialZoom);
    setPosition({ x: 0, y: 0 });
    if (onZoomChange) onZoomChange(initialZoom);
  }, [initialZoom, onZoomChange, setPosition]);

  // Handle wheel zoom with pointer-centered zooming and directional movement
  const handleWheel = useCallback(
    (e: React.WheelEvent<HTMLDivElement>) => {
      // Always handle directional panning first with highest priority

      // Handle Shift + scroll: Horizontal panning (highest priority)
      if (e.shiftKey && !e.ctrlKey && !e.metaKey) {
        e.preventDefault();
        e.stopPropagation();
        e.nativeEvent.preventDefault();
        e.nativeEvent.stopPropagation();

        const panSpeed = 2;
        // Use deltaX for horizontal mouse scroll, deltaY for vertical scroll converted to horizontal
        const horizontalDelta = e.deltaX !== 0 ? e.deltaX : e.deltaY;
        setPosition((prevPosition) => {
          const newPosition = {
            x: prevPosition.x - horizontalDelta * panSpeed,
            y: prevPosition.y, // Keep Y unchanged
          };
          return applyConstraints(newPosition);
        });
        return;
      }

      // Handle Ctrl/Cmd + scroll: Pinch-to-zoom gesture from trackpad
      if ((e.ctrlKey || e.metaKey) && !e.shiftKey) {
        e.preventDefault();
        e.stopPropagation();
        e.nativeEvent.preventDefault();
        e.nativeEvent.stopPropagation();

        // Get pointer position relative to the container
        const rect = e.currentTarget.getBoundingClientRect();
        const pointerX = e.clientX - rect.left;
        const pointerY = e.clientY - rect.top;

        // Determine zoom direction
        const zoomDirection = e.deltaY < 0 ? 1 : -1;

        // Get current zoom from ref for consistency
        const currentZoom = zoomRef.current;

        // Use same zoom intensity as regular scroll for consistency
        const zoomIntensity = 0.02 * (currentZoom < 1 ? 0.8 : 1.0);

        // Calculate new zoom with easing
        const newZoom = Math.max(
          minZoom,
          currentZoom * (1 + zoomDirection * zoomIntensity),
        );

        // Update zoom state first
        setZoom(newZoom);
        if (onZoomChange) onZoomChange(newZoom);

        // Update zoom ref immediately
        zoomRef.current = newZoom;

        // Calculate how the point under the pointer should move to maintain position
        setPosition((prevPosition) => {
          const scaleFactor = newZoom / currentZoom;
          const dx = pointerX - (pointerX - prevPosition.x) * scaleFactor;
          const dy = pointerY - (pointerY - prevPosition.y) * scaleFactor;

          return applyConstraints({ x: dx, y: dy });
        });
        return;
      }

      // Default zoom behavior (no modifier keys)
      // Get pointer position relative to the container
      const rect = e.currentTarget.getBoundingClientRect();
      const pointerX = e.clientX - rect.left;
      const pointerY = e.clientY - rect.top;

      // Determine zoom direction
      const zoomDirection = e.deltaY < 0 ? 1 : -1;

      // Reduced zoom intensity for more friction and control
      const zoomIntensity = 0.02 * (zoom < 1 ? 0.8 : 1.0);

      // Get current zoom from ref for consistency
      const currentZoom = zoomRef.current;

      // Calculate new zoom with easing
      const newZoom = Math.max(
        minZoom,
        currentZoom * (1 + zoomDirection * zoomIntensity),
      );

      // Update zoom state first
      setZoom(newZoom);
      if (onZoomChange) onZoomChange(newZoom);

      // Update zoom ref immediately
      zoomRef.current = newZoom;

      // Calculate how the point under the pointer should move to maintain position
      setPosition((prevPosition) => {
        // Calculate the position change needed to keep the pointer at the same logical position
        const scaleFactor = newZoom / currentZoom;
        const dx = pointerX - (pointerX - prevPosition.x) * scaleFactor;
        const dy = pointerY - (pointerY - prevPosition.y) * scaleFactor;

        return applyConstraints({ x: dx, y: dy });
      });
    },
    [zoom, minZoom, onZoomChange, setPosition, applyConstraints],
  );

  // Track the last touch positions for calculating pinch gestures
  const [lastTouchDistance, setLastTouchDistance] = useState<number | null>(
    null,
  );

  // Function to reset touch tracking
  const resetTouchTracking = useCallback(
    (e?: React.TouchEvent<HTMLDivElement>) => {
      if (e) {
        e.stopPropagation(); // Stop event from bubbling up
      }
      setLastTouchDistance(null);
    },
    [],
  );

  // Calculate distance between two touch points
  const getTouchDistance = (touches: React.TouchList): number => {
    if (touches.length < 2) return 0;

    const dx = touches[0].clientX - touches[1].clientX;
    const dy = touches[0].clientY - touches[1].clientY;
    return Math.sqrt(dx * dx + dy * dy);
  };

  // Handle trackpad pinch-to-zoom gestures with pointer-centered zooming
  const handleGestureChange = useCallback(
    (e: React.TouchEvent<HTMLDivElement>) => {
      const touches = e.touches;

      // Handle multi-touch pinch gesture
      if (touches.length >= 2) {
        // Prevent default browser behavior for multi-touch gestures
        e.preventDefault();
        e.stopPropagation();

        const currentDistance = getTouchDistance(touches);

        // Calculate the center point between the two touches
        const centerX = (touches[0].clientX + touches[1].clientX) / 2;
        const centerY = (touches[0].clientY + touches[1].clientY) / 2;

        // Get container position
        const rect = e.currentTarget.getBoundingClientRect();
        const pointerX = centerX - rect.left;
        const pointerY = centerY - rect.top;

        if (lastTouchDistance !== null && currentDistance > 0) {
          // Calculate the zoom factor based on the ratio of distances
          const ratio = currentDistance / lastTouchDistance;

          // Only process if there's a meaningful change to avoid noise
          if (Math.abs(ratio - 1) > 0.01) {
            // Convert ratio to zoom direction similar to wheel events
            const zoomDirection = ratio > 1 ? 1 : -1;

            // Get current zoom from ref for consistency
            const currentZoom = zoomRef.current;

            // Use the same zoom intensity calculation as wheel scroll for consistency
            const zoomIntensity = 0.02 * (currentZoom < 1 ? 0.8 : 1.0);

            // Scale the intensity based on how much the pinch changed
            const intensityMultiplier = Math.abs(ratio - 1) * 10; // Amplify the gesture
            const adjustedIntensity = Math.min(
              zoomIntensity * intensityMultiplier,
              0.1,
            );

            // Calculate new zoom using same logic as wheel zoom
            const newZoom = Math.max(
              minZoom,
              currentZoom * (1 + zoomDirection * adjustedIntensity),
            );

            // Update zoom state first to ensure consistency
            setZoom(newZoom);
            if (onZoomChange) onZoomChange(newZoom);

            // Update zoom ref immediately for next calculation
            zoomRef.current = newZoom;

            // Calculate how the point under the pointer should move to maintain position
            // Using same logic as wheel zoom
            setPosition((prevPosition) => {
              const scaleFactor = newZoom / currentZoom;
              const dx = pointerX - (pointerX - prevPosition.x) * scaleFactor;
              const dy = pointerY - (pointerY - prevPosition.y) * scaleFactor;

              return applyConstraints({ x: dx, y: dy });
            });
          }
        }

        setLastTouchDistance(currentDistance);
      } else {
        // Reset when not using multi-touch
        setLastTouchDistance(null);
      }
    },
    [lastTouchDistance, minZoom, onZoomChange, setPosition, applyConstraints],
  );

  // Handle keyboard shortcuts for zooming
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === '=' || e.key === '+') {
          e.preventDefault();
          zoomIn();
        } else if (e.key === '-') {
          e.preventDefault();
          zoomOut();
        } else if (e.key === '0') {
          e.preventDefault();
          resetZoom();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [zoomIn, zoomOut, resetZoom]);

  // Handle Safari-specific gesture events and wheel events with capture phase
  useEffect(() => {
    // Only apply if containerRef is provided
    if (!containerRef?.current) return;

    const canvasElement = containerRef.current;

    // Safari-specific gesture events
    const handleGestureStart = (e: Event) => {
      e.preventDefault();
      e.stopPropagation(); // Stop event from bubbling up
    };

    const handleGestureChange = (e: Event) => {
      e.preventDefault();
      e.stopPropagation(); // Stop event from bubbling up
    };

    const handleGestureEnd = (e: Event) => {
      e.preventDefault();
      e.stopPropagation(); // Stop event from bubbling up
    };

    // Handle wheel events in capture phase - handle ALL modifier combinations here
    const handleWheelCapture = (e: WheelEvent) => {
      // Handle Shift + wheel for horizontal panning
      if (e.shiftKey && !e.ctrlKey && !e.metaKey) {
        e.preventDefault();
        e.stopPropagation();

        const panSpeed = 2;
        // Use deltaX for horizontal mouse scroll, deltaY for vertical scroll converted to horizontal
        const horizontalDelta = e.deltaX !== 0 ? e.deltaX : e.deltaY;
        setPosition((prevPosition) => {
          const newPosition = {
            x: prevPosition.x - horizontalDelta * panSpeed,
            y: prevPosition.y, // Keep Y unchanged
          };
          return applyConstraints(newPosition);
        });
        return;
      }

      // Handle Ctrl/Cmd + wheel: Pinch-to-zoom gesture from trackpad (without Shift)
      if ((e.ctrlKey || e.metaKey) && !e.shiftKey) {
        e.preventDefault();
        e.stopPropagation();

        // Get pointer position relative to the container
        const rect = canvasElement.getBoundingClientRect();
        const pointerX = e.clientX - rect.left;
        const pointerY = e.clientY - rect.top;

        // Determine zoom direction
        const zoomDirection = e.deltaY < 0 ? 1 : -1;

        // Get current zoom from ref for consistency
        const currentZoom = zoomRef.current;

        // Use same zoom intensity as regular scroll for consistency
        const zoomIntensity = 0.02 * (currentZoom < 1 ? 0.8 : 1.0);

        // Calculate new zoom with easing
        const newZoom = Math.max(
          minZoom,
          currentZoom * (1 + zoomDirection * zoomIntensity),
        );

        // Update zoom state first
        setZoom(newZoom);
        if (onZoomChange) onZoomChange(newZoom);

        // Update zoom ref immediately
        zoomRef.current = newZoom;

        // Calculate how the point under the pointer should move to maintain position
        setPosition((prevPosition) => {
          const scaleFactor = newZoom / currentZoom;
          const dx = pointerX - (pointerX - prevPosition.x) * scaleFactor;
          const dy = pointerY - (pointerY - prevPosition.y) * scaleFactor;

          return applyConstraints({ x: dx, y: dy });
        });
        return;
      }
    };

    // Add event listeners specifically to the canvas element
    canvasElement.addEventListener('gesturestart', handleGestureStart, {
      passive: false,
    });
    canvasElement.addEventListener('gesturechange', handleGestureChange, {
      passive: false,
    });
    canvasElement.addEventListener('gestureend', handleGestureEnd, {
      passive: false,
    });
    canvasElement.addEventListener('wheel', handleWheelCapture, {
      passive: false,
      capture: true,
    });

    return () => {
      // Clean up event listeners
      canvasElement.removeEventListener('gesturestart', handleGestureStart);
      canvasElement.removeEventListener('gesturechange', handleGestureChange);
      canvasElement.removeEventListener('gestureend', handleGestureEnd);
      canvasElement.removeEventListener('wheel', handleWheelCapture, {
        capture: true,
      });
    };
  }, [containerRef, minZoom, onZoomChange, setPosition, applyConstraints]);

  return {
    zoom,
    setZoom,
    zoomIn,
    zoomOut,
    resetZoom,
    handleWheel,
    handleGestureChange,
    resetTouchTracking,
    zoomPercentage,
    triggerBounceBack: triggerBounceBackIfNeeded,
  };
}
