import { useEffect, useState, useCallback } from 'react';
import {
  <PERSON>,
  MousePointer,
  Plus,
  PenTool,
  Pencil,
  Move,
  Grab,
  MessageCircle,
} from 'lucide-react';
import { SelectedTool } from '../types/drawing-types';
import { useTakeoffStore } from '../store/takeoff-store';
import {
  getCursorColorClass,
  shouldShowCrosshair,
} from '../utils/cursor-utils';

interface CursorPosition {
  x: number;
  y: number;
}

interface CustomCursorProps {
  selectedTool: SelectedTool;
  isSpacebarPanning?: boolean;
  isMiddleMousePanning?: boolean;
  isDragging?: boolean;
  isHoveringDrawing?: boolean;
  isDrawing?: boolean;
}

export function CustomCursor({
  selectedTool,
  isSpacebarPanning = false,
  isMiddleMousePanning = false,
  isDragging = false,
  isHoveringDrawing = false,
  isDrawing = false,
}: CustomCursorProps) {
  const [position, setPosition] = useState<CursorPosition>({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(true);
  const [isOverCanvas, setIsOverCanvas] = useState(true);
  const { isEditMode } = useTakeoffStore();

  // Throttled mouse position update for better performance
  const updatePosition = useCallback((e: MouseEvent) => {
    setPosition({ x: e.clientX, y: e.clientY });

    // Check if mouse is over canvas area
    const target = e.target as HTMLElement;
    const canvasElement = target.closest('.canvas-custom-cursor');
    setIsOverCanvas(!!canvasElement);
  }, []);

  useEffect(() => {
    let animationFrame: number;

    const handleMouseMove = (e: MouseEvent) => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }

      animationFrame = requestAnimationFrame(() => {
        updatePosition(e);
      });
    };

    const handleMouseEnter = () => setIsVisible(true);
    const handleMouseLeave = () => {
      setIsVisible(false);
      setIsOverCanvas(false);
    };

    // Add event listeners
    window.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseenter', handleMouseEnter);
    document.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
      window.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseenter', handleMouseEnter);
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [updatePosition]);

  // Determine which icon to show based on tool and context
  const getCursorIcon = () => {
    // Handle panning states first (highest priority)
    if (isSpacebarPanning || isMiddleMousePanning || selectedTool === 'pan') {
      return isDragging ? (
        <Grab size={20} strokeWidth={2} className="text-gray-700" />
      ) : (
        <Hand size={20} strokeWidth={1.5} className="text-gray-700" />
      );
    }

    // Handle select tool with contextual states
    if (selectedTool === 'select') {
      if (isHoveringDrawing || isDragging) {
        return <Move size={20} strokeWidth={1.5} className="text-blue-600" />;
      }
      return (
        <MousePointer size={20} strokeWidth={1.5} className="text-gray-700" />
      );
    }

    // Handle drawing tools
    if (!isEditMode) {
      // In view mode, show default pointer
      return (
        <MousePointer size={20} strokeWidth={1.5} className="text-gray-400" />
      );
    }

    // Get icon color for drawing tools
    const iconColor = getCursorColorClass(isDrawing, isDragging);

    switch (selectedTool) {
      case 'rectangle':
      case 'circle':
      case 'ellipse':
        return <Plus size={20} strokeWidth={1.5} className={iconColor} />;
      case 'point-to-point':
      case 'curve':
        return <PenTool size={20} strokeWidth={1.5} className={iconColor} />;
      case 'freehand':
        return <Pencil size={20} strokeWidth={1.5} className={iconColor} />;
      case 'comment':
        return (
          <MessageCircle size={20} strokeWidth={1.5} className={iconColor} />
        );
      default:
        return (
          <MousePointer size={20} strokeWidth={1.5} className="text-gray-700" />
        );
    }
  };

  // Don't render if cursor is not visible or not over canvas
  if (!isVisible || !isOverCanvas) {
    return null;
  }

  return (
    <div
      className="fixed pointer-events-none z-[99999] transition-opacity duration-150"
      style={{
        left: position.x,
        top: position.y,
        transform:
          selectedTool === 'point-to-point' || selectedTool === 'curve'
            ? 'translate(-0%, -0%)'
            : selectedTool === 'freehand'
              ? 'translate(-0%, -100%)'
              : 'translate(-50%, -50%)',
      }}
    >
      <div className="relative">
        {/* Main cursor icon */}
        <div className="drop-shadow-lg">{getCursorIcon()}</div>

        {/* Crosshair overlay for drawing tools when hovering over drawings */}
        {isHoveringDrawing && shouldShowCrosshair(selectedTool) && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border border-gray-400 rounded-full bg-transparent" />
          </div>
        )}

        {/* Active drawing indicator */}
        {isDrawing && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
        )}
      </div>
    </div>
  );
}
