import { But<PERSON> } from '@/components/ui/button';
import { ZoomIn, ZoomOut, RefreshCw, Edit, Eye } from 'lucide-react'; // Added Edit and Eye icons
import { useTakeoffStore } from '../store/takeoff-store';
import { ExportModal } from './ExportModal';

interface CanvasToolbarProps {
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  zoomPercentage: number;
  minZoom?: number;
  takeoffId: string;
}

/**
 * Top toolbar component for the canvas with zoom controls and sidebar toggle
 */
export function CanvasToolbar({
  zoomIn,
  zoomOut,
  resetZoom,
  zoomPercentage,
  minZoom = 0.3,
  takeoffId,
}: CanvasToolbarProps) {
  const { isEditMode, setIsEditMode } = useTakeoffStore();

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-20 flex h-12 items-center gap-1 bg-background border border-border rounded-full px-2 shadow-lg w-fit">
      {/* Edit/View Mode Toggle Button */}
      <Button
        variant={isEditMode ? 'default' : 'ghost'}
        size="sm"
        className={`h-8 px-3 rounded-full ${
          isEditMode
            ? 'bg-primary text-white hover:bg-primary/90'
            : 'hover:bg-muted'
        }`}
        onClick={() => setIsEditMode(!isEditMode)}
        title={isEditMode ? 'Switch to View Mode' : 'Switch to Edit Mode'}
      >
        {isEditMode ? (
          <>
            <Eye className="h-4 w-4 mr-1" />
            View
          </>
        ) : (
          <>
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </>
        )}
      </Button>

      <ExportModal takeoffId={takeoffId} />

      {/* Separator */}
      <div className="w-px h-6 bg-border mx-1" />

      {/* Zoom Controls */}
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        onClick={zoomOut}
        disabled={zoomPercentage <= minZoom * 100}
        title="Zoom Out (Ctrl+-)"
      >
        <ZoomOut className="h-4 w-4" />
        <span className="sr-only">Zoom Out</span>
      </Button>
      <span className="text-xs text-muted-foreground px-1 min-w-[3rem] text-center">
        {zoomPercentage}%
      </span>
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        onClick={zoomIn}
        title="Zoom In (Ctrl++)"
      >
        <ZoomIn className="h-4 w-4" />
        <span className="sr-only">Zoom In</span>
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        onClick={resetZoom}
        title="Reset Zoom (Ctrl+0)"
      >
        <RefreshCw className="h-4 w-4" />
        <span className="sr-only">Reset Zoom</span>
      </Button>
    </div>
  );
}
