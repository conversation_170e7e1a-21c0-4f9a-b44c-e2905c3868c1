import { Image as KonvaImage, Rect, Text } from 'react-konva';
import useImage from 'use-image';
import { URLImageProps } from '../types/drawing-types';
import { useEffect } from 'react';
import { useTakeoffStore } from '../store/takeoff-store';

/**
 * Component to display an image from a URL in a Konva canvas
 */
export function URLImage({
  src,
  width,
  height,
  handleStageClick,
  handleCanvasContextMenu,
}: URLImageProps) {
  // Only try to load image if src is provided
  // Using proper CORS parameters with empty src guard
  const { setIsCanvasLoaded } = useTakeoffStore((state) => state);
  const [image, status] = useImage(src && src.trim() !== '' ? src : '');

  useEffect(() => {
    if (src && status === 'loaded') {
      setIsCanvasLoaded(true);
    } else {
      setIsCanvasLoaded(false);
    }
  }, [setIsCanvasLoaded, src, status]);

  // Display the image to fill the entire canvas (architectural plan should cover the full drawable area)
  const getImageDimensions = () => {
    return {
      width,
      height,
      x: 0,
      y: 0,
    };
  };

  // Only calculate dimensions if image is loaded
  const imageDims = image
    ? getImageDimensions()
    : { width: 0, height: 0, x: 0, y: 0 };

  // Return the appropriate component based on loading status
  if (status === 'loaded' && image) {
    return (
      <KonvaImage
        image={image}
        width={imageDims.width}
        height={imageDims.height}
        x={imageDims.x}
        y={imageDims.y}
        onClick={handleStageClick}
        onTap={handleStageClick}
        onContextMenu={handleCanvasContextMenu}
      />
    );
  }

  // Loading or error state with more detailed error message
  const errorMessage =
    !src || src.trim() === ''
      ? 'No image source provided'
      : status === 'loading'
        ? 'Loading Worksheet...'
        : `Error Loading Worksheet${src ? ': ' + src.substring(0, 30) + '...' : ''}`;

  return (
    <>
      <Rect
        x={width / 4}
        y={height / 2 - 20}
        width={width / 2}
        height={40}
        fill="#f0f0f0"
        cornerRadius={5}
        onClick={handleStageClick}
        onTap={handleStageClick}
        onContextMenu={handleCanvasContextMenu}
      />
      <Text
        x={width / 4}
        y={height / 2 - 10}
        width={width / 2}
        height={20}
        text={errorMessage}
        fontSize={16}
        fontFamily="Arial"
        fill={status === 'loading' ? '#333333' : '#e74c3c'}
        align="center"
        onClick={handleStageClick}
        onTap={handleStageClick}
        onContextMenu={handleCanvasContextMenu}
      />
    </>
  );
}
