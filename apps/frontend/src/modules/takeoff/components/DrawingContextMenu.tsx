import { toast } from '@/lib/toast';
import { <PERSON><PERSON>, Sciss<PERSON>, Trash2 } from 'lucide-react';
import { useDeleteDrawing } from '../api/mutations';
import { useTakeoffStore } from '../store/takeoff-store';
import { Drawing } from '../types/drawing';
import { useUndoRedo } from '../hooks/useUndoRedo';

interface DrawingContextMenuProps {
  drawing: Drawing;
  onClose: () => void;
}

/**
 * Context menu component for drawings with copy, cut, and delete options
 */
export function DrawingContextMenu({
  drawing,
  onClose,
}: DrawingContextMenuProps) {
  const { setClipboardData, selectedImage, isEditMode } = useTakeoffStore();
  const deleteDrawingMutation = useDeleteDrawing();
  const { recordAction } = useUndoRedo();

  const handleCopy = () => {
    setClipboardData({
      drawings: [drawing], // Wrap single drawing in array
      operation: 'copy',
    });
    toast.success('Drawing copied to clipboard');
    onClose();
  };

  const handleCut = () => {
    if (!isEditMode) {
      toast.error('Cannot cut drawing: Edit mode required');
      return;
    }

    if (!selectedImage?.id) {
      toast.error('Cannot cut drawing: No image selected');
      return;
    }

    setClipboardData({
      drawings: [drawing], // Wrap single drawing in array
      operation: 'cut',
    });

    // Delete the original drawing
    deleteDrawingMutation.mutate(
      {
        drawingId: drawing.id,
        blueprintImageId: selectedImage.id,
      },
      {
        onSuccess: () => {
          // Record the delete action for undo/redo (cut is essentially a delete)
          recordAction({
            type: 'delete',
            data: {
              deletedDrawings: [drawing],
            },
          });
          toast.success('Drawing cut to clipboard');
          onClose();
        },
        onError: (error) => {
          toast.error(`Failed to cut drawing: ${error.message}`);
          // Clear clipboard data if delete failed
          setClipboardData(null);
          onClose();
        },
      },
    );
  };

  const handleDelete = () => {
    if (!isEditMode) {
      toast.error('Cannot delete drawing: Edit mode required');
      return;
    }

    if (!selectedImage?.id) {
      toast.error('Cannot delete drawing: No image selected');
      return;
    }

    deleteDrawingMutation.mutate(
      {
        drawingId: drawing.id,
        blueprintImageId: selectedImage.id,
      },
      {
        onSuccess: () => {
          // Record the delete action for undo/redo
          recordAction({
            type: 'delete',
            data: {
              deletedDrawings: [drawing],
            },
          });
          toast.success('Drawing deleted successfully');
          onClose();
        },
        onError: (error) => {
          toast.error(`Failed to delete drawing: ${error.message}`);
          onClose();
        },
      },
    );
  };

  return (
    <>
      <div className="py-1">
        <button
          className="flex w-full items-center px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={handleCopy}
        >
          <Copy className="mr-2 h-4 w-4" />
          Copy
        </button>
        <button
          className={`flex w-full items-center px-3 py-2 text-sm ${
            isEditMode
              ? 'hover:bg-gray-100 dark:hover:bg-gray-800'
              : 'opacity-50 cursor-not-allowed'
          }`}
          onClick={handleCut}
          disabled={!isEditMode}
          title={!isEditMode ? 'Edit mode required' : 'Cut drawing'}
        >
          <Scissors className="mr-2 h-4 w-4" />
          Cut
        </button>
        <button
          className={`flex w-full items-center px-3 py-2 text-sm text-red-600 dark:text-red-400 ${
            isEditMode
              ? 'hover:bg-red-50 dark:hover:bg-red-950'
              : 'opacity-50 cursor-not-allowed'
          }`}
          onClick={handleDelete}
          disabled={deleteDrawingMutation.isPending || !isEditMode}
          title={!isEditMode ? 'Edit mode required' : 'Delete drawing'}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </button>
      </div>
    </>
  );
}
