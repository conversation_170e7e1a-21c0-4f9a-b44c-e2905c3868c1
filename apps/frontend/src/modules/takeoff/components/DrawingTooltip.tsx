import React from 'react';
import { Drawing } from '../types/drawing';
import { ScaleInfo, PaperSize } from '@repo/component-summary';
import { getDrawingMeasurements } from '../utils/drawing-measurements';

interface DrawingTooltipProps {
  visible: boolean;
  position: { x: number; y: number };
  drawing: Drawing | null;
  scale: ScaleInfo | null;
  paperSize: PaperSize | null;
}

/**
 * Tooltip component that displays measurement information for drawings on hover
 * Follows the mouse cursor and shows different content based on drawing type
 */
export function DrawingTooltip({
  visible,
  position,
  drawing,
  scale,
  paperSize,
}: DrawingTooltipProps) {
  if (!visible || !drawing || !scale || !paperSize) {
    return null;
  }

  const measurements = getDrawingMeasurements(drawing, scale, paperSize);

  if (!measurements) {
    return null;
  }

  // Offset the tooltip slightly from the cursor to avoid obstruction
  const tooltipStyle: React.CSSProperties = {
    position: 'fixed',
    left: position.x + 12,
    top: position.y + 12,
    backgroundColor: '#09090b',
    color: '#fafafa',
    padding: '8px 12px',
    borderRadius: '6px',
    fontSize: '12px',
    fontFamily: 'Inter, system-ui, sans-serif',
    fontWeight: 600,
    lineHeight: '1.4',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    zIndex: 1000,
    pointerEvents: 'none', // Prevent tooltip from interfering with mouse events
    maxWidth: '200px',
    whiteSpace: 'nowrap' as const,
  };

  return (
    <div style={tooltipStyle}>
      {/* Component name heading if drawing belongs to a component */}
      {drawing.component?.name && (
        <div
          style={{
            fontSize: '11px',
            fontWeight: 500,
            color: '#a1a1aa',
            marginBottom: '4px',
            textTransform: 'uppercase' as const,
            letterSpacing: '0.5px',
          }}
        >
          {drawing.component.name}
        </div>
      )}

      {/* Primary measurement (area, length or count) */}
      <div style={{ marginBottom: measurements.secondary ? '2px' : '0' }}>
        {measurements.type === 'area'
          ? 'Area: '
          : measurements.type === 'length'
            ? 'Length: '
            : 'Count: '}
        {measurements.primary}
      </div>

      {/* Secondary measurement (perimeter for closed shapes) */}
      {measurements.secondary && <div>Perimeter: {measurements.secondary}</div>}

      {/* Additional details (optional) */}
      {measurements.details && measurements.details.length > 0 && (
        <div
          style={{
            fontSize: '10px',
            color: '#a1a1aa',
            marginTop: '4px',
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            paddingTop: '4px',
          }}
        >
          {measurements.details.map((detail, index) => (
            <div key={index}>{detail}</div>
          ))}
        </div>
      )}
    </div>
  );
}
