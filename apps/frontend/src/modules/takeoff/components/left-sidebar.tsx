'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarRail,
  useSidebar,
} from '@/components/ui/sidebar';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { ArrowLeft, PanelLeftOpen, Search, Settings2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import { useComponentManagement } from '../hooks/useComponentManagement';
import { AddComponentModal } from './add-component-modal';
import { CollapsibleComponentSection } from './CollapsibleComponentSection';
import { DeleteComponentDialog } from './DeleteComponentDialog';

export function LeftSidebar({ projectName }: { projectName?: string }) {
  const router = useRouter();
  const { toggleLeftSidebar } = useSidebar();

  const {
    selectedComponentItem,
    componentSections,
    isModalOpen,
    editingComponent,
    currentComponentTypeForModal,
    isDeleteDialogVisible,
    deleteComponentMutationIsPending: deleteComponentMutationStatus,
    handleSelectComponent,
    handleShowComponent,
    handleLocateComponent,
    handleEditComponent,
    handleDeleteComponent,
    confirmDeleteComponent,
    handleToggleSectionOpen,
    handleAddComponent,
    handleSaveComponentFromModal,
    closeModalAndReset,
    closeDeleteDialog,
    activeTab,
    setActiveTab,
    searchTerm,
    setSearchTerm,
    setIsColorPickerOpen,
    isColorPickerOpen,
    // Component summary functionality
    summaryData,
    toggleComponentSummary,
  } = useComponentManagement();

  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchTerm(localSearchTerm);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [localSearchTerm, setSearchTerm]);

  return (
    <>
      <Sidebar
        side="left"
        className="border-r"
        style={{ '--sidebar-width': '18.5rem' } as React.CSSProperties}
      >
        {/* Sidebar Header: Contains back button and "Components" title */}
        <SidebarHeader className="h-14 border-b">
          <div className="flex items-center gap-2 px-2">
            {' '}
            {/* Reverted to original flex layout */}
            <Button
              onClick={() => router.push('/dashboard/home')}
              variant="ghost"
              size="icon"
              className="h-8 w-8 cursor-pointer"
              aria-label="Back to dashboard"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h2
              className="text-sm font-medium truncate max-w-44"
              title={projectName}
            >
              {projectName}
            </h2>
            <Button
              onClick={toggleLeftSidebar}
              variant="ghost"
              size="icon"
              className="ml-auto h-8 w-8"
              aria-label="Toggle sidebar"
            >
              <PanelLeftOpen className="h-4 w-4" />
            </Button>
          </div>
        </SidebarHeader>

        {/* Sidebar Content: Contains all UI elements below the header */}
        <SidebarContent className="flex flex-col gap-4 p-4 overflow-y-auto">
          {/* Tabs for "Takeoff aggregate" and "Page specific" - Card Style */}
          <div className="bg-background border border-border rounded-xl p-3 shadow-sm">
            <ToggleGroup
              type="single"
              defaultValue="specific"
              value={activeTab}
              onValueChange={(value) => {
                if (value) setActiveTab(value as 'aggregate' | 'specific');
              }}
              className="w-full grid grid-cols-2 gap-1"
            >
              <ToggleGroupItem
                value="aggregate"
                className="text-xs h-8 rounded-lg data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
              >
                Takeoff aggregate
              </ToggleGroupItem>
              <ToggleGroupItem
                value="specific"
                className="text-xs h-8 rounded-lg data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
              >
                Page specific
              </ToggleGroupItem>
            </ToggleGroup>
          </div>

          {/* Search section - Card Style */}
          <div className="bg-background border border-border rounded-xl p-3 shadow-sm">
            <div className="flex items-center gap-2 mb-3">
              <Settings2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <h3 className="text-xs font-medium whitespace-nowrap">
                All Components
              </h3>
            </div>
            <div className="relative">
              <Input
                placeholder="Type to search"
                className="h-9 pl-9 text-xs rounded-lg border-border"
                value={localSearchTerm}
                onChange={(e) => setLocalSearchTerm(e.target.value)}
                aria-label="Search components"
              />
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            </div>
          </div>

          {/* Collapsible sections for Surface, Edge, and Point - Card Style */}
          <div className="bg-background border border-border rounded-xl p-3 shadow-sm">
            <div className="space-y-2">
              {componentSections.map((section) => (
                <CollapsibleComponentSection
                  key={section.type}
                  section={section}
                  onToggleOpen={() => handleToggleSectionOpen(section.type)}
                  onAddComponent={handleAddComponent}
                  selectedComponentItem={selectedComponentItem}
                  onSelectComponent={handleSelectComponent}
                  onShowComponent={handleShowComponent}
                  onLocateComponent={handleLocateComponent}
                  onEditComponent={handleEditComponent}
                  onDeleteComponent={handleDeleteComponent}
                  summaryData={summaryData}
                  onToggleComponentSummary={toggleComponentSummary}
                />
              ))}
            </div>
          </div>
        </SidebarContent>

        {/* Sidebar Rail (if used for icons or collapsed state) */}
        <SidebarRail />
      </Sidebar>
      {/* Add Component Modal */}
      <AddComponentModal
        isOpen={isModalOpen}
        onClose={closeModalAndReset}
        onSave={handleSaveComponentFromModal}
        componentType={
          editingComponent?.geometryType || currentComponentTypeForModal
        }
        initialData={editingComponent || undefined}
        setIsColorPickerOpen={setIsColorPickerOpen}
        isColorPickerOpen={isColorPickerOpen}
      />
      {/* Delete Confirmation Dialog */}
      <DeleteComponentDialog
        isOpen={isDeleteDialogVisible}
        onOpenChange={(open) => {
          if (!open) {
            closeDeleteDialog();
          }
          // else: opening is handled by the hook setting isDeleteDialogVisible to true
        }}
        onConfirm={confirmDeleteComponent}
        isPending={deleteComponentMutationStatus}
      />
    </>
  );
}
