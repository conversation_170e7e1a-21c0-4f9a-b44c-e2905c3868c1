import { Button } from '@/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { RadioGroup } from '@/components/ui/radio-group';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronDown, ChevronRight, Plus } from 'lucide-react';
import React from 'react';
import { Component as ComponentData, GeometryType } from '../types/component'; // Renamed to avoid conflict
import { TakeoffComponentItem } from './TakeoffComponentItem';
import { ComponentSummaryData } from '@repo/component-summary';

// Use the main Component type for individual component items
export type ComponentItemData = ComponentData;

// Interface for the data structure of each collapsible section
export interface ComponentSectionData {
  type: GeometryType;
  title: string;
  items: ComponentItemData[];
  isLoading: boolean;
  isOpen: boolean;
  icon: React.ElementType; // Lucide icon component
}

// Props for the CollapsibleComponentSection helper component
export interface CollapsibleComponentSectionProps {
  section: ComponentSectionData;
  onToggleOpen: () => void;
  onAddComponent: (type: GeometryType) => void;
  selectedComponentItem: ComponentItemData | null;
  onSelectComponent: (item: ComponentItemData) => void;
  onShowComponent: (itemId: number) => void;
  onLocateComponent: (itemId: number) => void;
  onEditComponent: (itemId: number) => void;
  onDeleteComponent: (itemId: number) => void;
  // Component summary props
  summaryData: Map<number, ComponentSummaryData>;
  onToggleComponentSummary: (componentId: number) => void;
}

// Helper component to render each collapsible section (Surface, Edge, Point)
export const CollapsibleComponentSection: React.FC<
  CollapsibleComponentSectionProps
> = ({
  section,
  onToggleOpen,
  onAddComponent,
  selectedComponentItem,
  onSelectComponent,
  onShowComponent,
  onLocateComponent,
  onEditComponent,
  onDeleteComponent,
  summaryData,
  onToggleComponentSummary,
}) => {
  const SectionIcon = section.icon;

  return (
    <Collapsible
      open={section.isOpen}
      onOpenChange={onToggleOpen}
      className="w-full"
    >
      {/* Trigger for opening/closing the collapsible section */}
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="w-full flex justify-between items-center px-2 py-1.5 h-auto hover:bg-muted/50"
        >
          <div className="flex items-center gap-2">
            <SectionIcon className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{section.title}</span>
          </div>
          {section.isOpen ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      </CollapsibleTrigger>
      {/* Content of the collapsible section */}
      <CollapsibleContent className="pt-2 pb-1 pl-2 pr-2 space-y-2 border-l border-transparent ml-2">
        {' '}
        {/* Indented content */}
        {section.isLoading ? (
          // Loading state: Show skeleton loaders
          <>
            <Skeleton className="h-8 w-full rounded" />
            <Skeleton className="h-8 w-full rounded" />
          </>
        ) : section.items.length === 0 ? (
          // Empty state: Show message and "Add Component" button
          <div className="text-center py-3 text-xs text-muted-foreground">
            <p>No {section.title.toLowerCase()} components defined.</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 w-full h-8 text-xs"
              onClick={() => onAddComponent(section.type)}
            >
              <Plus className="mr-1.5 h-3.5 w-3.5" /> Add {section.title}
            </Button>
          </div>
        ) : (
          <>
            {' '}
            {/* Content with items */}
            <RadioGroup
              value={selectedComponentItem?.id?.toString() || ''}
              className="space-y-1"
            >
              {' '}
              {/* Removed onValueChange */}
              {section.items.map((item) => (
                <TakeoffComponentItem
                  key={item.id}
                  item={item}
                  isSelected={selectedComponentItem?.id === item.id}
                  onSelect={onSelectComponent} // Directly pass the handler
                  onShow={onShowComponent}
                  onLocate={onLocateComponent}
                  onEdit={onEditComponent}
                  onDelete={onDeleteComponent}
                  // Component summary props
                  summaryData={summaryData.get(item.id)}
                  onToggleSummary={onToggleComponentSummary}
                  // onExpand can be added here if needed for individual items
                />
              ))}
            </RadioGroup>
            {/* "Add Component" button always visible if items exist */}
            <Button
              variant="outline"
              size="sm"
              className="mt-2 w-full h-8 text-xs"
              onClick={() => onAddComponent(section.type)}
            >
              <Plus className="mr-1.5 h-3.5 w-3.5" /> Add {section.title}
            </Button>
          </>
        )}
      </CollapsibleContent>
    </Collapsible>
  );
};
