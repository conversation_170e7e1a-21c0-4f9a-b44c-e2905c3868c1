import { Rect } from 'react-konva';
import { useState } from 'react';
import Konva from 'konva';
import { calculateSelectionBounds } from '../utils/canvas-utils';
import { Drawing } from '../types/drawing';
import { useTakeoffStore } from '../store/takeoff-store';

interface SelectionBoundsProps {
  selectedDrawings: Drawing[];
  zoom: number;
  selectedTool?: string;
  groupDragOffset?: { x: number; y: number } | null;
  onContextMenu?: (
    e: any,
    bounds: { x: number; y: number; width: number; height: number },
  ) => void;
  onGroupDragStart?: () => void;
  onGroupDragMove?: (deltaX: number, deltaY: number) => void;
  onGroupDragEnd?: (deltaX: number, deltaY: number) => void;
}

/**
 * Component to render selection bounds around multiple selected drawings
 */
export function SelectionBounds({
  selectedDrawings,
  zoom,
  selectedTool,
  groupDragOffset,
  onContextMenu,
  onGroupDragStart,
  onGroupDragMove,
  onGroupDragEnd,
}: SelectionBoundsProps) {
  const { isShiftPressed, isEditMode } = useTakeoffStore();
  const [dragStartPos, setDragStartPos] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [isHovering, setIsHovering] = useState(false);

  if (selectedDrawings.length <= 1) return null;

  // Convert drawings to the format expected by calculateSelectionBounds
  const drawingData = selectedDrawings.map((drawing) => {
    const config = drawing.config;
    return {
      x: parseFloat(config.x || '0'),
      y: parseFloat(config.y || '0'),
      width: config.width ? parseFloat(config.width) : undefined,
      height: config.height ? parseFloat(config.height) : undefined,
      radius: config.radius ? parseFloat(config.radius) : undefined,
      radiusX: config.radiusX ? parseFloat(config.radiusX) : undefined,
      radiusY: config.radiusY ? parseFloat(config.radiusY) : undefined,
      points: config.points ? JSON.parse(config.points) : undefined,
    };
  });

  const baseBounds = calculateSelectionBounds(drawingData);
  if (!baseBounds) return null;

  // Apply group drag offset to bounds for real-time visual feedback
  const bounds = groupDragOffset
    ? {
        x: baseBounds.x + groupDragOffset.x,
        y: baseBounds.y + groupDragOffset.y,
        width: baseBounds.width,
        height: baseBounds.height,
      }
    : baseBounds;

  const strokeWidth = 2 / zoom;
  const dashArray = [4 / zoom, 4 / zoom];

  // Enable dragging only in select mode when in edit mode
  const isDraggable = selectedTool === 'select' && isEditMode;

  return (
    <>
      {/* Invisible clickable area for context menu and dragging */}
      <Rect
        x={bounds.x}
        y={bounds.y}
        width={bounds.width}
        height={bounds.height}
        fill="transparent"
        stroke="transparent"
        strokeWidth={0}
        listening={!isShiftPressed}
        draggable={isDraggable}
        onContextMenu={(e) => {
          if (onContextMenu) {
            onContextMenu(e, bounds);
          }
        }}
        onMouseEnter={(e: Konva.KonvaEventObject<MouseEvent>) => {
          // Change cursor to move when hovering over selection bounds in select mode
          if (selectedTool === 'select' && isEditMode) {
            setIsHovering(true);
            const stage = e.target.getStage();
            if (stage) {
              stage.container().style.cursor = 'move';
            }
          }
        }}
        onMouseLeave={(e: Konva.KonvaEventObject<MouseEvent>) => {
          // Reset cursor when leaving selection bounds
          setIsHovering(false);
          if (selectedTool === 'select') {
            const stage = e.target.getStage();
            if (stage) {
              stage.container().style.cursor = 'default';
            }
          }
        }}
        onDragStart={(e: Konva.KonvaEventObject<DragEvent>) => {
          if (isDraggable) {
            setDragStartPos({ x: e.target.x(), y: e.target.y() });
            onGroupDragStart?.();
          }
        }}
        onDragMove={(e: Konva.KonvaEventObject<DragEvent>) => {
          // Provide real-time visual feedback by moving drawings during drag
          if (isDraggable && dragStartPos) {
            const currentX = e.target.x();
            const currentY = e.target.y();
            const deltaX = currentX - dragStartPos.x;
            const deltaY = currentY - dragStartPos.y;

            // Call a handler for real-time movement (if provided)
            onGroupDragMove?.(deltaX, deltaY);
          }
        }}
        onMouseUp={(e: Konva.KonvaEventObject<MouseEvent>) => {
          // Handle drag end via mouse up if we have a drag start position
          if (isDraggable && dragStartPos) {
            const currentX = e.target.x();
            const currentY = e.target.y();
            const deltaX = currentX - dragStartPos.x;
            const deltaY = currentY - dragStartPos.y;

            // Reset the bounds position BEFORE calling the handler
            e.target.x(dragStartPos.x);
            e.target.y(dragStartPos.y);

            // Reset cursor to default after group drag
            const stage = e.target.getStage();
            if (stage) {
              stage.container().style.cursor = 'default';
            }

            // Reset hover state
            setIsHovering(false);

            // Only call the handler if there was actual movement
            if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
              onGroupDragEnd?.(deltaX, deltaY);
            }

            setDragStartPos(null);
          }
        }}
        onDragEnd={(e: Konva.KonvaEventObject<DragEvent>) => {
          // Backup handler - mouseup is the primary handler
          // Reset cursor as fallback
          const stage = e.target.getStage();
          if (stage) {
            stage.container().style.cursor = 'default';
          }
          setIsHovering(false);
        }}
      />

      {/* Visible bounding box border */}
      <Rect
        x={bounds.x}
        y={bounds.y}
        width={bounds.width}
        height={bounds.height}
        fill={
          dragStartPos
            ? 'rgba(0, 123, 255, 0.1)'
            : isHovering
              ? 'rgba(0, 123, 255, 0.05)'
              : 'transparent'
        } // Light blue fill when dragging or hovering
        stroke="#007bff"
        strokeWidth={
          dragStartPos
            ? strokeWidth * 2
            : isHovering
              ? strokeWidth * 1.5
              : strokeWidth
        } // Thicker border when dragging or hovering
        dash={dashArray}
        listening={false}
      />
    </>
  );
}
