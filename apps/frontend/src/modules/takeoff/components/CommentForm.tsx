import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface CommentFormProps {
  x: number; // Screen coordinates for positioning
  y: number;
  onSave: (text: string) => void;
  onCancel: () => void;
  isVisible: boolean;
}

/**
 * Comment form popup component
 * Displays a minimal form at the specified screen coordinates for adding comments
 */
export const CommentForm: React.FC<CommentFormProps> = ({
  x,
  y,
  onSave,
  onCancel,
  isVisible,
}) => {
  const [text, setText] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-focus when form becomes visible
  useEffect(() => {
    if (isVisible && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isVisible]);

  // Reset text when form is hidden
  useEffect(() => {
    if (!isVisible) {
      setText('');
    }
  }, [isVisible]);

  const handleCancel = useCallback(() => {
    setText('');
    onCancel();
  }, [onCancel]);

  // Handle global Escape key to close the form
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        event.preventDefault();
        handleCancel();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => document.removeEventListener('keydown', handleEscapeKey);
    }
  }, [handleCancel, isVisible, onCancel]);

  const handleSave = () => {
    if (text.trim()) {
      onSave(text.trim());
      setText('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    }
    if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-3 min-w-[280px]"
      style={{
        left: `${x}px`,
        top: `${y}px`,
        transform: 'translate(-50%, -100%)', // Center horizontally, position above click point
      }}
    >
      <div className="space-y-3">
        <Textarea
          ref={textareaRef}
          value={text}
          onChange={(e) => setText(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Add a comment..."
          className="min-h-[80px] resize-none border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
          maxLength={500}
        />
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="h-8 px-3 text-sm"
          >
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            disabled={!text.trim()}
            className="h-8 px-3 text-sm bg-blue-600 hover:bg-blue-700"
          >
            Save
          </Button>
        </div>
      </div>
      {/* Small arrow pointing down to click point */}
      <div
        className="absolute top-full left-1/2 transform -translate-x-1/2"
        style={{
          width: 0,
          height: 0,
          borderLeft: '6px solid transparent',
          borderRight: '6px solid transparent',
          borderTop: '6px solid white',
          filter: 'drop-shadow(0 1px 1px rgba(0,0,0,0.1))',
        }}
      />
    </div>
  );
};
