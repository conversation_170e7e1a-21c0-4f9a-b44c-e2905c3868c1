import { Layer, Text } from 'react-konva';
import { URLImage } from './URLImage';
import { CanvasDimensions } from '../types/drawing-types';
import Konva from 'konva';

interface ImageLayerProps {
  selectedImagePath?: string;
  dimensions: CanvasDimensions;
  handleStageClick: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  handleCanvasContextMenu?: (e: Konva.KonvaEventObject<PointerEvent>) => void;
}

/**
 * Layer component for displaying the blueprint image
 */
export function ImageLayer({
  selectedImagePath,
  dimensions,
  handleStageClick,
  handleCanvasContextMenu,
}: ImageLayerProps) {
  return (
    <Layer>
      {selectedImagePath ? (
        <URLImage
          src={selectedImagePath}
          width={dimensions.width}
          height={dimensions.height}
          handleStageClick={handleStageClick}
          handleCanvasContextMenu={handleCanvasContextMenu}
        />
      ) : (
        <Text
          x={0}
          y={dimensions.height / 2 - 10}
          width={dimensions.width}
          text="No image selected"
          fontSize={16}
          fontFamily="Arial"
          fill="#666666"
          align="center"
          onClick={handleStageClick}
          onTap={handleStageClick}
          onContextMenu={handleCanvasContextMenu}
        />
      )}
    </Layer>
  );
}
