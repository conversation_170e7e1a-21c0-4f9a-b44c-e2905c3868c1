import { ReactNode } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';

interface ToolbarButtonProps {
  icon: ReactNode;
  tooltip: string;
  disabled?: boolean;
  'aria-label': string;
  onClick: () => void;
  isSelected?: boolean;
  className?: string;
}

/**
 * Reusable toolbar button component with consistent styling and tooltip
 */
export function ToolbarButton({
  icon,
  tooltip,
  disabled,
  onClick,
  isSelected = false,
  className,
  ...props
}: ToolbarButtonProps) {
  const baseClassName = `w-10 h-10 p-0 rounded-lg transition-colors ${
    disabled ? 'opacity-50 cursor-not-allowed' : ''
  }`;

  const selectedClassName = isSelected
    ? 'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white'
    : 'hover:bg-muted';

  const finalClassName = `${baseClassName} ${selectedClassName} ${className || ''}`;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClick}
          disabled={disabled}
          aria-label={props['aria-label']}
          className={finalClassName}
        >
          {icon}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{tooltip}</p>
      </TooltipContent>
    </Tooltip>
  );
}
