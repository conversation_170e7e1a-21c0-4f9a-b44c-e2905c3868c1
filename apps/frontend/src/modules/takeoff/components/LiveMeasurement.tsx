import { DrawingShape } from '../types/drawing-types';
import { ScaleInfo, PaperSize } from '@repo/component-summary';
import {
  MeasurementBadge,
  drawingShapeToConfig,
  calculateMeasurementPositionFromShape,
  checkMinimumSizeForShape,
  calculateMeasurementData,
} from '../utils/measurement-utils';

interface LiveMeasurementProps {
  currentDrawing: DrawingShape | null;
  scale: ScaleInfo;
  paperSize: PaperSize;
  zoom: number; // The zoom scale of the canvas
}

/**
 * Component to display live measurements while drawing shapes
 */
export function LiveMeasurement({
  currentDrawing,
  scale,
  paperSize,
  zoom,
}: LiveMeasurementProps) {
  if (!currentDrawing || currentDrawing.type === 'selection') {
    return null;
  }

  // Check minimum size requirements for different shape types
  if (!checkMinimumSizeForShape(currentDrawing)) {
    return null;
  }

  // Convert drawing shape to config format
  const config = drawingShapeToConfig(currentDrawing);

  // Calculate measurements using the dynamic measurements utility
  const { measurements, displayText } = calculateMeasurementData(
    config,
    scale,
    paperSize,
  );

  if (!measurements || !displayText) {
    return null;
  }

  // Calculate the position for the measurement badge
  const position = calculateMeasurementPositionFromShape(currentDrawing, zoom);

  return (
    <MeasurementBadge
      text={displayText}
      x={position.x}
      y={position.y}
      zoom={zoom}
    />
  );
}
