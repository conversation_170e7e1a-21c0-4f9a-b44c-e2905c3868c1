import { ClipboardPaste } from 'lucide-react';
import { useTakeoffStore } from '../store/takeoff-store';
import { useCreateDrawing } from '../api/mutations';
import { CreateDrawingPayload } from '../types/drawing';
import { toast } from '@/lib/toast';
import { areAllDrawingsWithinBounds } from '../utils/canvas-utils';
import { CanvasDimensions } from '../types/drawing-types';
import { useUndoRedo } from '../hooks/useUndoRedo';

interface CanvasContextMenuProps {
  onPaste: (x: number, y: number) => void;
  onClose: () => void;
  clickPosition: { x: number; y: number };
  dimensions: CanvasDimensions;
}

/**
 * Context menu component for canvas with paste option
 */
export function CanvasContextMenu({
  onPaste,
  onClose,
  clickPosition,
  dimensions,
}: CanvasContextMenuProps) {
  const { clipboardData, setClipboardData, selectedImage, isEditMode } =
    useTakeoffStore();
  const createDrawingMutation = useCreateDrawing();
  const { recordAction } = useUndoRedo();

  const handlePaste = () => {
    if (!isEditMode) {
      toast.error('Cannot paste: Edit mode required');
      return;
    }

    if (!clipboardData || clipboardData.drawings.length === 0) {
      toast.error('No drawings in clipboard');
      return;
    }

    if (!selectedImage?.id) {
      toast.error('Cannot paste: No image selected');
      return;
    }

    // Use the click position from the context menu
    const { x, y } = clickPosition;

    // Calculate the bounding box of all drawings to maintain relative positions
    const drawingsData = clipboardData.drawings.map((drawing) => ({
      x: parseFloat(drawing.config.x || '0'),
      y: parseFloat(drawing.config.y || '0'),
      width: drawing.config.width
        ? parseFloat(drawing.config.width)
        : undefined,
      height: drawing.config.height
        ? parseFloat(drawing.config.height)
        : undefined,
      radius: drawing.config.radius
        ? parseFloat(drawing.config.radius)
        : undefined,
      points: drawing.config.points
        ? JSON.parse(drawing.config.points)
        : undefined,
    }));

    // Find the top-left corner of the selection bounding box
    let minX = Infinity;
    let minY = Infinity;

    drawingsData.forEach((drawingData) => {
      if (drawingData.radius !== undefined) {
        // Circle drawing
        minX = Math.min(minX, drawingData.x - drawingData.radius);
        minY = Math.min(minY, drawingData.y - drawingData.radius);
      } else if (
        drawingData.width !== undefined &&
        drawingData.height !== undefined
      ) {
        // Rectangle drawing - handle negative dimensions
        const left = Math.min(drawingData.x, drawingData.x + drawingData.width);
        const top = Math.min(drawingData.y, drawingData.y + drawingData.height);
        minX = Math.min(minX, left);
        minY = Math.min(minY, top);
      } else if (
        drawingData.points !== undefined &&
        drawingData.points.length >= 2
      ) {
        // Freehand drawing - check all points
        for (let i = 0; i < drawingData.points.length; i += 2) {
          minX = Math.min(minX, drawingData.points[i]);
          minY = Math.min(minY, drawingData.points[i + 1]);
        }
      } else {
        // Handle other drawing types (like comments) - use their x,y position
        minX = Math.min(minX, drawingData.x);
        minY = Math.min(minY, drawingData.y);
      }
    });

    // Create drawing objects with new positions to check bounds
    const newDrawingsToCheck = clipboardData.drawings.map((drawing) => {
      const originalX = parseFloat(drawing.config.x || '0');
      const originalY = parseFloat(drawing.config.y || '0');

      // Calculate relative position from the top-left of the selection
      const relativeX = originalX - minX;
      const relativeY = originalY - minY;

      // Position relative to the click position
      const newX = x + relativeX;
      const newY = y + relativeY;

      // Handle freehand drawings - update all points
      let newPoints: number[] | undefined = undefined;
      if (drawing.config.points) {
        const originalPoints = JSON.parse(drawing.config.points);
        const offsetX = newX - originalX;
        const offsetY = newY - originalY;

        newPoints = [];
        for (let i = 0; i < originalPoints.length; i += 2) {
          newPoints.push(originalPoints[i] + offsetX);
          newPoints.push(originalPoints[i + 1] + offsetY);
        }
      }

      const transformedDrawing = {
        x: newX,
        y: newY,
        width: drawing.config.width
          ? parseFloat(drawing.config.width)
          : undefined,
        height: drawing.config.height
          ? parseFloat(drawing.config.height)
          : undefined,
        radius: drawing.config.radius
          ? parseFloat(drawing.config.radius)
          : undefined,
        points: newPoints,
        type: drawing.config.type, // Include type for special handling
      };

      return transformedDrawing;
    });

    // Check if all drawings will be within bounds after pasting
    if (!areAllDrawingsWithinBounds(newDrawingsToCheck, dimensions)) {
      toast.error("Drawing can't go out of plan extent");
      onClose();
      return;
    }

    // Create multiple drawings maintaining their relative positions
    const createPromises = clipboardData.drawings.map((drawing, index) => {
      const newDrawing = newDrawingsToCheck[index];

      const newDrawingConfig: Record<string, string> = {
        ...drawing.config,
        x: String(newDrawing.x),
        y: String(newDrawing.y),
      };

      // For freehand drawings, update the points with the new positions
      if (newDrawing.points) {
        newDrawingConfig.points = JSON.stringify(newDrawing.points);
      }

      const payload: CreateDrawingPayload = {
        blueprintImageId: selectedImage.id,
        config: newDrawingConfig,
      };

      // Preserve the original component from the copied/cut drawing
      if (drawing.componentId) {
        payload.componentId = drawing.componentId;
        payload._optimisticComponent = drawing.component || null;
      }

      return createDrawingMutation.mutateAsync(payload);
    });

    Promise.all(createPromises)
      .then((responses) => {
        const count = clipboardData.drawings.length;

        // Record the paste action for undo/redo
        if (clipboardData.operation === 'cut') {
          // Cut-paste operation - record as atomic move operation
          recordAction({
            type: 'cut-paste',
            data: {
              originalDrawings: clipboardData.drawings, // Original drawings with their positions
              newDrawings: responses.map((response) => response.data), // New drawings at pasted positions
            },
          });
        } else {
          // Copy-paste operation - record as create action (always uses array)
          recordAction({
            type: 'create',
            data: {
              drawings: responses.map((response) => response.data),
            },
          });
        }

        toast.success(`${count} drawing(s) pasted successfully`);
        // Clear clipboard if it was a cut operation
        if (clipboardData.operation === 'cut') {
          setClipboardData(null);
        }
        onPaste(x, y);
        onClose();
      })
      .catch((error) => {
        toast.error(`Failed to paste drawings: ${error.message}`);
        onClose();
      });
  };

  const hasClipboardData = !!clipboardData;
  const canPaste = hasClipboardData && isEditMode;

  return (
    <div className="py-1">
      <button
        className={`flex w-full items-center px-3 py-2 text-sm ${
          canPaste
            ? 'hover:bg-gray-100 dark:hover:bg-gray-800'
            : 'text-gray-400 cursor-not-allowed'
        }`}
        onClick={handlePaste}
        disabled={!canPaste}
        title={
          !isEditMode
            ? 'Edit mode required'
            : !hasClipboardData
              ? 'No drawings in clipboard'
              : 'Paste drawings here'
        }
      >
        <ClipboardPaste className="mr-2 h-4 w-4" />
        Paste Here
      </button>
    </div>
  );
}
