import { toast } from '@/lib/toast';
import { <PERSON><PERSON>, Sc<PERSON><PERSON>, Trash2 } from 'lucide-react';
import { useDeleteDrawing } from '../api/mutations';
import { useTakeoffStore } from '../store/takeoff-store';
import { Drawing } from '../types/drawing';
import { useUndoRedo } from '../hooks/useUndoRedo';

interface MultiSelectionContextMenuProps {
  selectedDrawings: Drawing[];
  onClose: () => void;
}

/**
 * Context menu component for multiple selected drawings with batch operations
 */
export function MultiSelectionContextMenu({
  selectedDrawings,
  onClose,
}: MultiSelectionContextMenuProps) {
  const { setClipboardData, selectedImage, clearSelection, isEditMode } =
    useTakeoffStore();
  const deleteDrawingMutation = useDeleteDrawing();
  const { recordAction } = useUndoRedo();

  const handleCopyAll = () => {
    // Copy all selected drawings
    if (selectedDrawings.length > 0) {
      setClipboardData({
        drawings: selectedDrawings,
        operation: 'copy',
      });
      toast.success(
        `${selectedDrawings.length} drawing(s) copied to clipboard`,
      );
    }
    onClose();
  };

  const handleCutAll = () => {
    if (!isEditMode) {
      toast.error('Cannot cut drawings: Edit mode required');
      return;
    }

    if (!selectedImage?.id) {
      toast.error('Cannot cut drawings: No image selected');
      return;
    }

    if (selectedDrawings.length === 0) {
      toast.error('No drawings selected');
      return;
    }

    // Cut all selected drawings
    setClipboardData({
      drawings: selectedDrawings,
      operation: 'cut',
    });

    // Delete all selected drawings
    const deletePromises = selectedDrawings.map((drawing) =>
      deleteDrawingMutation.mutateAsync({
        drawingId: drawing.id,
        blueprintImageId: selectedImage.id,
      }),
    );

    Promise.all(deletePromises)
      .then(() => {
        // Record the delete action for undo/redo (cut is essentially a delete)
        recordAction({
          type: 'delete',
          data: {
            deletedDrawings: selectedDrawings,
          },
        });
        toast.success(`${selectedDrawings.length} drawing(s) cut to clipboard`);
        clearSelection();
        onClose();
      })
      .catch((error) => {
        toast.error(`Failed to cut drawings: ${error.message}`);
        setClipboardData(null);
        onClose();
      });
  };

  const handleDeleteAll = () => {
    if (!isEditMode) {
      toast.error('Cannot delete drawings: Edit mode required');
      return;
    }

    if (!selectedImage?.id) {
      toast.error('Cannot delete drawings: No image selected');
      return;
    }

    if (selectedDrawings.length === 0) {
      toast.error('No drawings selected');
      return;
    }

    // Delete all selected drawings
    const deletePromises = selectedDrawings.map((drawing) =>
      deleteDrawingMutation.mutateAsync({
        drawingId: drawing.id,
        blueprintImageId: selectedImage.id,
      }),
    );

    Promise.all(deletePromises)
      .then(() => {
        // Record the delete action for undo/redo
        recordAction({
          type: 'delete',
          data: {
            deletedDrawings: selectedDrawings,
          },
        });
        toast.success(
          `${selectedDrawings.length} drawing(s) deleted successfully`,
        );
        clearSelection();
        onClose();
      })
      .catch((error) => {
        toast.error(`Failed to delete drawings: ${error.message}`);
        onClose();
      });
  };

  return (
    <>
      <div className="py-1">
        <div className="px-3 py-2 text-xs text-gray-500 border-b">
          {selectedDrawings.length} drawing(s) selected
        </div>
        <button
          className="flex w-full items-center px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={handleCopyAll}
        >
          <Copy className="mr-2 h-4 w-4" />
          Copy All
        </button>
        <button
          className={`flex w-full items-center px-3 py-2 text-sm ${
            isEditMode
              ? 'hover:bg-gray-100 dark:hover:bg-gray-800'
              : 'opacity-50 cursor-not-allowed'
          }`}
          onClick={handleCutAll}
          disabled={deleteDrawingMutation.isPending || !isEditMode}
          title={!isEditMode ? 'Edit mode required' : 'Cut all drawings'}
        >
          <Scissors className="mr-2 h-4 w-4" />
          Cut All
        </button>
        <button
          className={`flex w-full items-center px-3 py-2 text-sm text-red-600 dark:text-red-400 ${
            isEditMode
              ? 'hover:bg-red-50 dark:hover:bg-red-950'
              : 'opacity-50 cursor-not-allowed'
          }`}
          onClick={handleDeleteAll}
          disabled={deleteDrawingMutation.isPending || !isEditMode}
          title={!isEditMode ? 'Edit mode required' : 'Delete all drawings'}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete All
        </button>
      </div>
    </>
  );
}
