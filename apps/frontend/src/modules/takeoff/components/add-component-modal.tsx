import { ErrorMessage } from '@/components/error-message';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea'; // Added Textarea
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect } from 'react';
import { HexColorPicker } from 'react-colorful';
import { <PERSON>mit<PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';
import { z } from 'zod';
import { Component as ComponentData, GeometryType } from '../types/component';

// Define Zod schema for validation
const addComponentSchema = z.object({
  componentName: z.string().min(1, { message: 'Component name is required.' }),
  commonToAllPages: z
    .enum(['yes', 'no'], {
      required_error: 'Please select an option.',
    })
    .optional(), // Made optional to handle cases where field is hidden
  description: z.string().optional(),
  color: z.string().optional(), // Added color, optional
  pointType: z.enum(['circle', 'square', 'triangle']).optional(),
});

export type AddComponentFormData = z.infer<typeof addComponentSchema>;

// export type ComponentType = 'surface' | 'edge' | 'point'; // Replaced by GeometryType

interface AddComponentModalProps {
  isOpen: boolean;
  onClose: () => void;
  // onSave will now pass the full form data and the component type
  onSave: (
    data: AddComponentFormData,
    componentType: GeometryType,
    isEditing: boolean,
    originalComponentId?: number,
    color?: string,
  ) => void;
  componentType: GeometryType | null; // Type of component being added/edited
  initialData?: ComponentData; // For pre-filling the form in edit mode
  setIsColorPickerOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isColorPickerOpen: boolean;
}

export const AddComponentModal: React.FC<AddComponentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  componentType,
  initialData,
  isColorPickerOpen,
  setIsColorPickerOpen,
}) => {
  const form = useForm<AddComponentFormData>({
    resolver: zodResolver(addComponentSchema),
    defaultValues: {
      componentName: initialData?.name || '',
      commonToAllPages:
        initialData?.selectionType === 'allPages' ? 'yes' : 'no',
      description: initialData?.description || '',
      color: initialData?.color || '#aabbcc', // Default color if not provided
      pointType: (initialData?.geometricData as any)?.pointType || 'circle',
    },
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = form;

  // Reset form when modal opens, or when initialData or componentType changes
  useEffect(() => {
    if (isOpen) {
      if (initialData && componentType === initialData.geometryType) {
        // If editing and initialData is provided and matches current componentType
        reset({
          componentName: initialData.name,
          commonToAllPages:
            initialData.selectionType === 'allPages' ? 'yes' : 'no',
          description: initialData.description || '',
          color: initialData.color || '#aabbcc',
          pointType: (initialData.geometricData as any)?.pointType || 'circle',
        });
      } else {
        // If creating, or if initialData is stale (e.g., componentType changed)
        reset({
          componentName: '',
          commonToAllPages: 'no', // Default for new components
          description: '',
          color: '#aabbcc', // Default for new components, though picker is hidden
          pointType: 'circle',
        });
      }
    }
  }, [isOpen, initialData, componentType, reset]);

  const onSubmit: SubmitHandler<AddComponentFormData> = (data) => {
    if (isColorPickerOpen) {
      setIsColorPickerOpen(false);
      return;
    }

    if (componentType) {
      // If the commonToAllPages field is hidden, ensure we use the correct value
      const formData = {
        ...data,
        commonToAllPages: shouldHideCommonToAllPagesField
          ? ('yes' as const)
          : data.commonToAllPages || ('no' as const),
      };

      onSave(
        formData,
        componentType,
        !!initialData,
        initialData?.id,
        formData.color,
      );
      // onClose(); // Closing the modal should be handled by the onSave implementation after API call
    }
  };

  if (!componentType && !initialData) return null; // Only render if we have a type or initial data for edit

  // Determine the effective component type for display and logic
  const effectiveComponentType = initialData?.geometryType || componentType;
  if (!effectiveComponentType) return null; // Should not happen if logic above is correct

  // Determine if we should hide the "Common to all pages" field
  // Hide it when editing a component that is already common to all pages
  const shouldHideCommonToAllPagesField =
    initialData && initialData.selectionType === 'allPages';

  const typeDisplayNames: Record<GeometryType, string> = {
    surface: 'Surface',
    edge: 'Edge',
    point: 'Point',
  };

  const modalTitle = initialData
    ? `Edit ${typeDisplayNames[effectiveComponentType]} Component`
    : `Create New ${typeDisplayNames[effectiveComponentType]} Component`;
  const saveButtonText = initialData ? 'Save Changes' : 'Create Component';

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) onClose();
      }}
    >
      <DialogContent
        onInteractOutside={(event) => {
          // Close the popover manually when clicking outside modal
          if (isColorPickerOpen) {
            setIsColorPickerOpen(false);
            event.preventDefault(); // prevent the modal from closing if needed
          }
        }}
        className="sm:max-w-[480px]"
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {modalTitle}
          </DialogTitle>
          <DialogDescription className="mt-1">
            {initialData
              ? 'Edit the details of your component.'
              : 'Create a new component for your takeoff.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          {' '}
          {/* Spread form methods */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 py-4">
            <FormField
              control={control}
              name="componentName"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel
                    htmlFor="component-name"
                    className="text-sm font-medium"
                  >
                    Component Name
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="component-name"
                      placeholder="Enter component name"
                      className="h-10 w-full"
                      {...field}
                    />
                  </FormControl>
                  <ErrorMessage error={errors.componentName} />
                </FormItem>
              )}
            />

            {!shouldHideCommonToAllPagesField && (
              <FormField
                control={control}
                name="commonToAllPages"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-medium">
                      Common to all pages
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex items-center space-x-4 pt-1"
                      >
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="yes" id="common-yes" />
                          </FormControl>
                          <FormLabel
                            htmlFor="common-yes"
                            className="font-normal"
                          >
                            Yes
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="no" id="common-no" />
                          </FormControl>
                          <FormLabel
                            htmlFor="common-no"
                            className="font-normal"
                          >
                            No
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage>
                      {errors.commonToAllPages?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />
            )}

            {effectiveComponentType === 'point' && (
              <FormField
                control={control}
                name="pointType"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel
                      htmlFor="pointType"
                      className="text-sm font-medium"
                    >
                      Point Shape
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select point shape" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="circle">Circle</SelectItem>
                        <SelectItem value="square">Square</SelectItem>
                        <SelectItem value="triangle">Triangle</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage>{errors.pointType?.message}</FormMessage>
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={control}
              name="description"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel
                    htmlFor="component-description"
                    className="text-sm font-medium"
                  >
                    Description (Optional)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      id="component-description"
                      placeholder="Enter component description"
                      className="min-h-[80px] w-full" // Adjusted class for textarea
                      {...field}
                    />
                  </FormControl>
                  <ErrorMessage error={errors.description} />
                </FormItem>
              )}
            />

            {initialData && ( // Only show color picker in edit mode
              <FormField
                control={control}
                name="color"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel
                      htmlFor="component-color"
                      className="text-sm font-medium"
                    >
                      Component Color
                    </FormLabel>
                    <FormControl>
                      <Popover
                        open={isColorPickerOpen}
                        onOpenChange={setIsColorPickerOpen}
                        modal={true}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal h-10"
                          >
                            <div className="flex items-center">
                              <div
                                className="h-4 w-4 rounded-sm border mr-2"
                                style={{
                                  backgroundColor: field.value || 'transparent',
                                }}
                              />
                              {field.value ? (
                                <span
                                  style={{
                                    color: field.value,
                                    paddingLeft: '2px',
                                    paddingRight: '2px',
                                  }}
                                >
                                  {field.value}
                                </span>
                              ) : (
                                'Pick a color'
                              )}
                            </div>
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          onInteractOutside={(e) => {
                            const target = e.target as HTMLElement;

                            // Check if the target is a button with type="button" (cancel) or type="submit"
                            const isModalButton = target.closest(
                              'button[type="button"], button[type="submit"]',
                            );

                            if (isModalButton) {
                              e.preventDefault();
                            }
                          }}
                          className="w-auto p-0"
                          align="start"
                        >
                          <div data-id="color-picker-content">
                            <HexColorPicker
                              color={field.value || ''}
                              onChange={field.onChange}
                            />
                            <div className="p-2 border-t border-border">
                              <Input
                                id="component-color-input-popover" // Changed id to avoid conflict if any
                                value={field.value || ''}
                                onChange={(e) => field.onChange(e.target.value)}
                                className="h-8 w-full"
                                placeholder="Hex color code"
                              />
                            </div>
                            {/* Content of the popover is now wrapped */}
                          </div>
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                    <FormMessage>{errors.color?.message}</FormMessage>
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (isColorPickerOpen) {
                    setIsColorPickerOpen(false);
                    return;
                  }

                  onClose();
                }}
                className="h-10"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="h-10 bg-primary hover:bg-primary/90 text-primary-foreground"
                // disabled={form.formState.isSubmitting} // Optionally disable while submitting
              >
                {/* {form.formState.isSubmitting ? 'Saving...' : saveButtonText} */}
                {saveButtonText}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
