'use client';

import { useState } from 'react';
import { Download, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useExportToExcel } from '../api/export-mutations';
import { useTakeoffStore } from '../store/takeoff-store';
import { toast } from '@/lib/toast';

interface ExportModalProps {
  takeoffId: string;
}

export function ExportModal({ takeoffId }: ExportModalProps) {
  const [open, setOpen] = useState(false);
  const [measurementView, setMeasurementView] = useState<
    'current-page' | 'aggregate'
  >('current-page');

  const { selectedImage } = useTakeoffStore();
  const exportMutation = useExportToExcel();

  const handleDownload = async () => {
    if (measurementView === 'current-page' && !selectedImage?.id) {
      toast.error('No current page selected for export');
      return;
    }

    try {
      await exportMutation.mutateAsync({
        takeoffId,
        measurementView,
        blueprintImageId:
          measurementView === 'current-page' ? selectedImage?.id : undefined,
      });

      setOpen(false);
      setMeasurementView('current-page');
    } catch {
      // Error handling is done in the mutation
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-3 rounded-full hover:bg-muted"
        >
          <Download className="h-4 w-4 mr-1" />
          Download
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Download as Excel</DialogTitle>
          <DialogDescription>
            Choose what you'd like to include in your Excel download.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Choose Measurement View
            </Label>
            <RadioGroup
              value={measurementView}
              onValueChange={(value) =>
                setMeasurementView(value as 'current-page' | 'aggregate')
              }
              className="space-y-3"
              disabled={exportMutation.isPending}
            >
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="current-page" id="current-page" />
                <Label
                  htmlFor="current-page"
                  className="font-normal cursor-pointer"
                >
                  Current Page
                  {!selectedImage?.id && (
                    <span className="text-muted-foreground text-xs ml-2">
                      (No page selected)
                    </span>
                  )}
                </Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="aggregate" id="aggregate" />
                <Label
                  htmlFor="aggregate"
                  className="font-normal cursor-pointer"
                >
                  Aggregate (Full Project)
                </Label>
              </div>
            </RadioGroup>
          </div>
        </div>
        <DialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={exportMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDownload}
            className="gap-2"
            disabled={
              exportMutation.isPending ||
              (measurementView === 'current-page' && !selectedImage?.id)
            }
          >
            {exportMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            {exportMutation.isPending ? 'Downloading...' : 'Download Excel'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
