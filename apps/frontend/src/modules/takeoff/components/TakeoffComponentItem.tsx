import React from 'react';
import clsx from 'clsx'; // Import clsx
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { RadioGroupItem } from '@/components/ui/radio-group';
import { Button } from '@/components/ui/button';
import { Plus, Eye, LocateFixed, MoreHorizontal, Minus } from 'lucide-react';
// Import ComponentData directly from the types file
import { Component as ComponentData } from '../types/component';
import { useTakeoffStore } from '../store/takeoff-store';
import { ComponentSummaryData } from '@repo/component-summary';
import { Drawing } from '../types/drawing';

export interface TakeoffComponentItemProps {
  item: ComponentData; // Use the full ComponentData type
  isSelected: boolean;
  onSelect: (item: ComponentData) => void;
  // Placeholder functions for icon actions - to be implemented later
  onExpand?: (itemId: number) => void; // Optional, if expand functionality is needed per item
  onShow: (itemId: number) => void;
  onLocate: (itemId: number) => void;
  onEdit: (itemId: number, isColorPicker?: boolean) => void;
  onDelete: (itemId: number) => void;
  // Component summary props
  summaryData?: ComponentSummaryData;
  onToggleSummary?: (componentId: number) => void;
}

export const TakeoffComponentItem: React.FC<TakeoffComponentItemProps> = ({
  item,
  isSelected,
  onSelect,
  onExpand,
  onShow,
  onLocate,
  onEdit,
  onDelete,
  summaryData,
  onToggleSummary,
}) => {
  const { locatedComponentId, setTooltip, canvasState } = useTakeoffStore();
  const isLocated = locatedComponentId === item.id;

  // Handle drawing hover for tooltip
  const handleDrawingHover = (drawing: Drawing) => {
    // Only show tooltip in view mode (!isEditMode)
    // if (isEditMode) return;
    let tooltipPosition = { x: 0, y: 0 };

    // Try to use the drawing's actual position from config if available
    if (
      drawing.config &&
      drawing.config.x !== undefined &&
      drawing.config.y !== undefined
    ) {
      // Find the canvas element to get its position
      const canvasElement = document.querySelector('canvas');
      if (canvasElement) {
        const canvasRect = canvasElement.getBoundingClientRect();

        // Get drawing position from config
        const drawingX = parseFloat(drawing.config.x || '0');
        const drawingY = parseFloat(drawing.config.y || '0');

        // Apply canvas transformation (zoom and pan)
        const transformedX =
          drawingX * canvasState.zoom + canvasState.position.x;
        const transformedY =
          drawingY * canvasState.zoom + canvasState.position.y;

        tooltipPosition = {
          x: canvasRect.left + transformedX,
          y: canvasRect.top + transformedY,
        };

        // Ensure tooltip stays within viewport
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        if (tooltipPosition.x > viewportWidth - 200) {
          tooltipPosition.x = viewportWidth - 200;
        }
        if (tooltipPosition.y > viewportHeight - 100) {
          tooltipPosition.y = viewportHeight - 100;
        }
        if (tooltipPosition.x < 0) {
          tooltipPosition.x = 20;
        }
        if (tooltipPosition.y < 0) {
          tooltipPosition.y = 20;
        }
      }
    }

    setTooltip({
      visible: true,
      position: tooltipPosition,
      drawing, // Now we have the full Drawing object
    });
  };

  // Handle drawing hover end for tooltip
  const handleDrawingHoverEnd = () => {
    setTooltip({
      visible: false,
      position: { x: 0, y: 0 },
      drawing: null,
    });
  };

  return (
    <TooltipProvider delayDuration={200}>
      <div className="space-y-1">
        {/* Component Item */}
        <div
          className={clsx(
            'flex items-center justify-between p-1.5 rounded-md hover:bg-muted/50 text-xs w-full cursor-pointer',
            isSelected && 'border border-primary bg-muted/30', // Add border and subtle bg when selected
          )}
          onClick={() => onSelect(item)} // Moved onClick to the main div
        >
          <div className="flex items-center gap-2 flex-grow min-w-0">
            <RadioGroupItem
              value={item.id.toString()}
              id={`radio-${item.id}`}
              checked={isSelected}
              // onClick is now on the parent div, but keep it for accessibility if needed, or remove if redundant
              // onClick={(e) => { e.stopPropagation(); onSelect(item); }}
              aria-label={`Select ${item.name}`}
              className="sr-only after:absolute after:inset-0 pointer-events-none"
            />
            <div
              className="w-3 h-3 rounded  flex-shrink-0"
              style={{ backgroundColor: item.color || '#cccccc' }} // Use item.color, fallback to gray
              title={`Color: ${item.color}`}
              onClick={(e) => {
                e.stopPropagation();
                onEdit(item.id, true);
              }}
            />
            <span className="line-clamp-2" title={item.name}>
              {item.name}
            </span>
          </div>

          <div className="flex items-center gap-0.5 ml-2">
            {onExpand && ( // Conditionally render expand if onExpand is provided
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => {
                      e.stopPropagation();
                      onExpand(item.id);
                    }}
                    aria-label="Expand"
                  >
                    <Plus className="h-3.5 w-3.5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Expand</p>
                </TooltipContent>
              </Tooltip>
            )}

            {/* Summary Toggle Button */}
            {summaryData && onToggleSummary && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => {
                      e.stopPropagation();
                      onToggleSummary(item.id);
                    }}
                    aria-label={
                      summaryData.isExpanded
                        ? 'Collapse summary'
                        : 'Expand summary'
                    }
                  >
                    {summaryData.isExpanded ? (
                      <Minus className="h-3.5 w-3.5" />
                    ) : (
                      <Plus className="h-3.5 w-3.5" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {summaryData.isExpanded
                      ? 'Collapse summary'
                      : 'Expand summary'}
                  </p>
                </TooltipContent>
              </Tooltip>
            )}

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={(e) => {
                    e.stopPropagation();
                    onShow(item.id);
                  }}
                  aria-label="Show"
                >
                  <Eye className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Show/Hide</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={clsx(
                    'h-6 w-6 cursor-pointer',
                    isLocated && 'bg-blue-100 hover:bg-blue-200',
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    onLocate(item.id);
                  }}
                  aria-label="Locate"
                >
                  <LocateFixed
                    className={clsx(
                      'h-3.5 w-3.5',
                      isLocated ? 'text-blue-600' : '',
                    )}
                  />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Locate</p>
              </TooltipContent>
            </Tooltip>

            <DropdownMenu>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      aria-label="More options"
                    >
                      <MoreHorizontal className="h-3.5 w-3.5" />
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <p>More options</p>
                </TooltipContent>
              </Tooltip>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(item.id);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(item.id);
                  }}
                  className="text-destructive focus:text-destructive focus:bg-destructive/10"
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Component Summary - Only show expanded content when toggled */}
        {summaryData && summaryData.isExpanded && (
          <div className="mt-2 border border-border rounded-md bg-gradient-to-br from-muted/30 to-muted/10 overflow-hidden">
            {/* Header with component info */}
            <div className="px-3 py-2 bg-muted/40 border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded border border-white/20"
                    style={{ backgroundColor: item.color || '#cccccc' }}
                  />
                  <h4 className="text-sm font-semibold text-foreground">
                    {item.name}
                  </h4>
                </div>
                <div className="text-xs text-muted-foreground px-2 py-1 bg-background/50 rounded">
                  {item.geometryType.charAt(0).toUpperCase() +
                    item.geometryType.slice(1)}
                </div>
              </div>
            </div>

            {/* Metrics section */}
            <div className="p-3 space-y-3">
              {/* Total metrics with proper labels */}
              <div className="space-y-2">
                <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  Summary
                </h5>
                <div className="bg-background/60 rounded-lg p-3 space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      {item.geometryType === 'surface'
                        ? 'Total Area:'
                        : item.geometryType === 'edge'
                          ? 'Total Length:'
                          : 'Total Count:'}
                    </span>
                    <span className="text-sm font-bold text-primary">
                      {summaryData.totalMetrics.formattedValue}{' '}
                      {summaryData.totalMetrics.unit}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Drawings:
                    </span>
                    <span className="text-sm font-medium">
                      {summaryData.drawingSummaryItems.length} item
                      {summaryData.drawingSummaryItems.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>
              </div>

              {/* Individual drawings breakdown - wrapped in accordion */}
              {summaryData.drawingSummaryItems.length > 0 && (
                <div className="space-y-2">
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="drawings" className="border-none">
                      <AccordionTrigger className="py-2 px-0 text-xs font-medium text-muted-foreground uppercase tracking-wide hover:no-underline">
                        View All Drawings (
                        {summaryData.drawingSummaryItems.length})
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-0">
                        <div className="space-y-1">
                          {summaryData.drawingSummaryItems.map(
                            (drawingSummaryItem, index) => {
                              // Find the corresponding full Drawing object for tooltip
                              const fullDrawing = summaryData.drawings.find(
                                (d) => d.id === drawingSummaryItem.id,
                              );

                              return (
                                <div
                                  key={drawingSummaryItem.id}
                                  className="flex justify-between items-center py-2 px-3 rounded-md bg-background/40 hover:bg-background/60 transition-colors"
                                  onMouseEnter={() =>
                                    fullDrawing &&
                                    handleDrawingHover(fullDrawing)
                                  }
                                  onMouseLeave={handleDrawingHoverEnd}
                                >
                                  <div className="flex items-center gap-2">
                                    <span className="text-xs font-mono bg-muted/60 px-1.5 py-0.5 rounded text-muted-foreground">
                                      #{index + 1}
                                    </span>
                                    <span className="text-xs font-medium">
                                      {drawingSummaryItem.displayId}
                                    </span>
                                  </div>
                                  <div className="text-right">
                                    <div className="text-xs font-medium">
                                      {drawingSummaryItem.measurement
                                        ? `${drawingSummaryItem.measurement.formattedValue} ${drawingSummaryItem.measurement.unit}`
                                        : item.geometryType === 'point'
                                          ? '1 point'
                                          : 'N/A'}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {item.geometryType === 'surface'
                                        ? 'Area'
                                        : item.geometryType === 'edge'
                                          ? 'Length'
                                          : 'Point'}
                                    </div>
                                  </div>
                                </div>
                              );
                            },
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}

              {summaryData.drawings.length === 0 && (
                <div className="text-center py-4">
                  <div className="text-xs text-muted-foreground italic">
                    No drawings found for this component
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Add drawings to see measurements here
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
};
