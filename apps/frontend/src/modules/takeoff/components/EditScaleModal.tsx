'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react'; // Removed useState as selectedPaperSize state is not used
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import type { ImageType } from '../store/takeoff-store';
import { ErrorMessage } from '@/components/error-message';
import { ARCH_PAPER_SIZES, DEFAULT_PAPER_SIZE } from '@repo/component-summary';
// import type { Unit } from '../types/shared'; // Unit type is implicitly handled

type ScaleType = ImageType['scale']; // Simplified: ImageType['scale'] is already non-nullable
type DimensionsType = ImageType['dimensions']; // Add dimensions type
const UNIT_OPTIONS = ['cm', 'ft', 'inch'] as const;

const editModalFormSchema = z.object({
  // Scale fields
  num_unit: z.enum(UNIT_OPTIONS, {
    required_error: 'Numerator unit is required.',
  }),
  den_metric: z.coerce
    .number({ invalid_type_error: 'Denominator must be a number.' })
    .positive({ message: 'Denominator must be a positive number.' }),
  den_unit: z.enum(UNIT_OPTIONS, {
    required_error: 'Denominator unit is required.',
  }),

  // Dimensions fields
  paperSize: z.string().optional(),
  width: z.coerce
    .number({ invalid_type_error: 'Width must be a number.' })
    .positive({ message: 'Width must be a positive number.' }),
  height: z.coerce
    .number({ invalid_type_error: 'Height must be a number.' })
    .positive({ message: 'Height must be a positive number.' }),
  unit: z.literal('inch' as const), // Unit is fixed to 'inch'
});

type EditModalFormValues = z.infer<typeof editModalFormSchema>;

// Paper size options for dropdown
const paperSizeOptions = Object.entries(ARCH_PAPER_SIZES).map(
  ([key, value]) => ({
    label: key.replace(/_/g, ' '), // "ARCH_A" → "ARCH A"
    value: key,
    width: value.width,
    height: value.height,
    unit: value.unit,
  }),
);

interface EditScaleModalProps {
  isOpen: boolean;
  onClose: () => void;
  image: ImageType;
  pageNumber: number; // Added for the title
  onSave: (
    imageId: string,
    newScale: ScaleType,
    newDimensions: DimensionsType,
  ) => Promise<void>;
  isSaving: boolean;
}

export function EditScaleModal({
  isOpen,
  onClose,
  image,
  pageNumber,
  onSave,
  isSaving,
}: EditScaleModalProps) {
  // selectedPaperSize state was removed as it's not directly used for display.
  // The form's "paperSize" field tracks the selection, and form.watch('paperSize') can be used if its value is needed elsewhere.

  const form = useForm<EditModalFormValues>({
    resolver: zodResolver(editModalFormSchema),
    defaultValues: {
      num_unit: 'inch', // Provide a default, will be overridden by useEffect
      den_metric: 1, // Provide a default
      den_unit: 'inch', // Provide a default
      paperSize: '',
      width: DEFAULT_PAPER_SIZE.width, // Default to ARCH_D size
      height: DEFAULT_PAPER_SIZE.height,
      unit: 'inch',
    },
  });

  // Helper function to find matching paper size
  const findMatchingPaperSize = (width: number, height: number): string => {
    for (const [key, paperSize] of Object.entries(ARCH_PAPER_SIZES)) {
      if (paperSize.width === width && paperSize.height === height) {
        return key;
      }

      // finding the default key
      if (
        paperSize.width === DEFAULT_PAPER_SIZE.width &&
        paperSize.height === DEFAULT_PAPER_SIZE.height
      ) {
        return key; // Return default size if no match found
      }
    }

    return '';
  };

  useEffect(() => {
    // image is guaranteed non-null if isOpen is true and modal is rendered due to PageItem logic + line 92 check
    if (isOpen) {
      const matchingPaperSize = findMatchingPaperSize(
        image.dimensions?.width,
        image.dimensions?.height,
      );

      form.reset({
        num_unit: image.scale?.num_unit,
        den_metric: image.scale?.den_metric,
        den_unit: image.scale?.den_unit,
        paperSize: matchingPaperSize,
        width: image.dimensions?.width || DEFAULT_PAPER_SIZE.width, // Default to common values if not present
        height: image.dimensions?.height || DEFAULT_PAPER_SIZE.height, // Default to common values if not present
        unit: 'inch', // Explicitly 'inch' as per schema default
      });
    }
  }, [isOpen, image, form]); // image is a dependency for re-calculating reset values if it changes while modal is open

  const handlePaperSizeChange = (selectedKey: string) => {
    // setSelectedPaperSize(selectedKey); // Form state handles the selected value
    const paperSizeInfo = paperSizeOptions.find((p) => p.value === selectedKey);
    if (paperSizeInfo) {
      form.setValue('width', paperSizeInfo.width);
      form.setValue('height', paperSizeInfo.height);
      form.setValue('unit', paperSizeInfo.unit as 'inch'); // Cast as 'inch'
    }
  };

  const onValidSubmit = async (data: EditModalFormValues) => {
    if (!image) return;

    const newScale: ScaleType = {
      num_metric: 1, // Fixed
      num_unit: data.num_unit,
      den_metric: data.den_metric,
      den_unit: data.den_unit,
    };

    const newDimensions: DimensionsType = {
      width: data.width,
      height: data.height,
      unit: data.unit,
    };

    await onSave(image.id, newScale, newDimensions);
  };

  if (!image) {
    return null;
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(openState) => {
        if (!openState) onClose();
      }}
    >
      <DialogContent className="sm:max-w-[550px]">
        {' '}
        {/* Slightly wider for better spacing with errors */}
        <DialogHeader>
          <DialogTitle>Edit Page {pageNumber} Scale & Dimensions</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onValidSubmit)}
            className="space-y-6 py-4"
          >
            {/* Scale Section */}
            <div>
              <h3 className="mb-2 text-lg font-medium">📏 Scale</h3>
              <div className="grid grid-cols-[1fr_1fr_auto_1fr_1fr] items-center gap-x-2 gap-y-1">
                {/* Numerator Metric (Fixed) */}
                <div className="col-span-1">
                  <Input
                    id="num_metric_display"
                    value="1"
                    disabled
                    aria-label="Numerator Metric (Fixed)"
                  />
                </div>
                {/* Numerator Unit */}
                <FormField
                  control={form.control}
                  name="num_unit"
                  render={({ field }) => (
                    <FormItem className="col-span-1">
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isSaving}
                      >
                        <FormControl>
                          <SelectTrigger
                            aria-label="Numerator Unit"
                            className="w-full"
                          >
                            <SelectValue placeholder="Unit" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {UNIT_OPTIONS.map((unit) => (
                            <SelectItem key={`num-${unit}`} value={unit}>
                              {unit}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
                <span className="text-center text-lg font-semibold mx-1">
                  :
                </span>
                {/* Denominator Metric */}
                <Input
                  id="den_metric"
                  disabled={isSaving}
                  placeholder="e.g., 16.00"
                  type="number"
                  step="any"
                  aria-label="Denominator Metric"
                  {...form.register('den_metric')}
                />
                {/* Denominator Unit */}
                <FormField
                  control={form.control}
                  name="den_unit"
                  render={({ field }) => (
                    <FormItem className="col-span-1">
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isSaving}
                      >
                        <FormControl>
                          <SelectTrigger
                            aria-label="Denominator Unit"
                            className="w-full"
                          >
                            <SelectValue placeholder="Unit" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {UNIT_OPTIONS.map((unit) => (
                            <SelectItem key={`den-${unit}`} value={unit}>
                              {unit}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator className="my-6" />

            {/* Dimensions Section */}
            <div>
              <h3 className="mb-4 text-lg font-medium">📐 Dimensions</h3>
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="paperSize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Architecture Size</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          handlePaperSizeChange(value);
                        }}
                        value={field.value}
                        disabled={isSaving}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a paper size" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {paperSizeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label} ({option.width}" x {option.height}
                              ")
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="width"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Width</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="any"
                            {...field}
                            disabled={isSaving}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="height"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Height</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="any"
                            {...field}
                            disabled={isSaving}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="unit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Unit</FormLabel>
                        <FormControl>
                          <Input {...field} disabled />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            {/* Common error messages */}
            {Object.keys(form.formState.errors).length > 0 && (
              <div className="mt-4 space-y-1">
                {Object.entries(form.formState.errors).map(
                  ([fieldName, error]) =>
                    error?.message && (
                      <ErrorMessage
                        key={fieldName}
                        error={{ message: String(error.message) }} // Ensure message is a string
                      />
                    ),
                )}
              </div>
            )}

            <DialogFooter className="pt-6">
              <DialogClose asChild>
                <Button type="button" variant="outline" disabled={isSaving}>
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={isSaving || !form.formState.isDirty}
              >
                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
