'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useSidebar } from '@/components/ui/sidebar';
import { PanelLeftOpen, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

interface LeftSidebarHeaderProps {
  projectName?: string;
}

export function LeftSidebarHeader({
  projectName = 'Components',
}: LeftSidebarHeaderProps) {
  const router = useRouter();
  const { toggleLeftSidebar } = useSidebar();

  return (
    <div className="fixed top-4 left-4 z-20 flex h-12 items-center gap-1 bg-background border border-border rounded-full px-2 shadow-lg w-fit">
      {/* Back Button */}
      <Button
        onClick={() => router.push('/dashboard/home')}
        variant="ghost"
        size="icon"
        className="h-8 w-8 cursor-pointer"
        aria-label="Back to dashboard"
        title="Back to Dashboard"
      >
        <ArrowLeft className="h-4 w-4" />
      </Button>

      {/* Separator */}
      <div className="w-px h-6 bg-border mx-1" />

      {/* Project Name */}
      <span
        className="text-sm font-medium px-2 text-foreground truncate max-w-32"
        title={projectName}
      >
        {projectName}
      </span>

      {/* Separator */}
      <div className="w-px h-6 bg-border mx-1" />

      {/* Toggle Sidebar Button */}
      <Button
        onClick={toggleLeftSidebar}
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        aria-label="Expand Components Sidebar"
        title="Expand Components Sidebar (Ctrl+B)"
      >
        <PanelLeftOpen className="h-4 w-4" />
      </Button>
    </div>
  );
}
