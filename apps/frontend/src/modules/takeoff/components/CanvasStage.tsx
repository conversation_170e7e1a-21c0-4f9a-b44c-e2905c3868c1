import { Stage } from 'react-konva';
import { useEffect, useRef, useState } from 'react';
import { ImageLayer } from './ImageLayer';
import { DrawingLayer } from './DrawingLayer';
import { CustomCursor } from './CustomCursor';
import {
  CanvasDimensions,
  CanvasPosition,
  DrawingShape,
  SelectedTool,
} from '../types/drawing-types';
import Konva from 'konva';
import { DrawingContextMenu } from './DrawingContextMenu';
import { CanvasContextMenu } from './CanvasContextMenu';
import { MultiSelectionContextMenu } from './MultiSelectionContextMenu';
import { Drawing } from '../types/drawing';
import { useTakeoffStore } from '../store/takeoff-store';
import { getRelativePointerPosition } from '../utils/canvas-utils';
import { useCustomCursor } from '../hooks/useCustomCursor';
import { DrawingTooltip } from './DrawingTooltip';

interface CanvasStageProps {
  dimensions: CanvasDimensions; // Full screen canvas dimensions
  imageDimensions?: CanvasDimensions; // Fixed image dimensions (optional, defaults to dimensions)
  zoom: number;
  position: CanvasPosition;
  setPosition: React.Dispatch<React.SetStateAction<CanvasPosition>>;
  selectedTool: SelectedTool;
  isShapeDragging: boolean;
  isSpacebarPanning: boolean;
  isMiddleMousePanning: boolean;
  currentDrawing: DrawingShape | null;
  selectedImagePath?: string;
  mousePositionRef: React.RefObject<{ x: number; y: number } | null>;
  fetchedDrawings?: any[];
  drawingsError: any;
  setIsShapeDragging: React.Dispatch<React.SetStateAction<boolean>>;
  handleMouseDown: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  handleMouseMove: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  handleMouseUp: (e?: Konva.KonvaEventObject<MouseEvent>) => void;
  handleDragEnd: (
    drawingId: number,
    event: Konva.KonvaEventObject<DragEvent>,
  ) => void;
  handleGroupDragStart: () => void;
  handleGroupDragMove: (deltaX: number, deltaY: number) => void;
  handleGroupDragEnd: (deltaX: number, deltaY: number) => void;
  handleDrawingSelect: (drawingId: number) => void;
  handleStageClick: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  groupDragOffset?: { x: number; y: number } | null;
  triggerBounceBack?: () => void;
}

/**
 * Main Konva Stage component for the canvas
 */
export function CanvasStage({
  dimensions,
  imageDimensions,
  zoom,
  position,
  setPosition,
  selectedTool,
  isShapeDragging,
  isSpacebarPanning,
  isMiddleMousePanning,
  currentDrawing,
  selectedImagePath,
  fetchedDrawings = [],
  drawingsError,
  setIsShapeDragging,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleDragEnd,
  handleGroupDragStart: originalHandleGroupDragStart,
  handleGroupDragMove: originalHandleGroupDragMove,
  handleGroupDragEnd: originalHandleGroupDragEnd,
  handleDrawingSelect,
  handleStageClick,
  mousePositionRef,
  groupDragOffset,
  triggerBounceBack,
}: CanvasStageProps) {
  const stageRef = useRef<Konva.Stage>(null);
  const {
    selectedDrawingIds,
    isEditMode,
    selectedImage,
    tooltip,
    setTooltip,
    paperSize,
  } = useTakeoffStore();

  // State to track if canvas is being dragged (for cursor changes)
  const [isGroupDragging, setIsGroupDragging] = useState(false);
  const [isCanvasDragging, setIsCanvasDragging] = useState(false);

  // Custom cursor hook
  const cursorState = useCustomCursor({
    selectedTool,
    currentDrawing,
    isShapeDragging,
    isGroupDragging,
    isSpacebarPanning,
    isMiddleMousePanning,
    _fetchedDrawings: fetchedDrawings,
    stageRef,
  });

  // Wrapper functions to manage group dragging state
  const handleGroupDragStart = () => {
    setIsGroupDragging(true);
    originalHandleGroupDragStart();
  };

  const handleGroupDragMove = (deltaX: number, deltaY: number) => {
    originalHandleGroupDragMove(deltaX, deltaY);
  };

  const handleGroupDragEnd = (deltaX: number, deltaY: number) => {
    setIsGroupDragging(false);
    originalHandleGroupDragEnd(deltaX, deltaY);
  };

  // Use imageDimensions for the image layer, fallback to dimensions if not provided
  const imageLayerDimensions = imageDimensions || dimensions;

  // Determine if we're in pan mode (either pan tool, spacebar panning, or middle mouse panning)
  const isPanMode =
    selectedTool === 'pan' || isSpacebarPanning || isMiddleMousePanning;

  // Determine cursor class based on pan mode and drag state
  const getCursorClass = () => {
    // Always use custom cursor container class to hide default cursor
    return 'canvas-custom-cursor';
  };
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    type: 'drawing' | 'canvas' | 'multiselection';
    drawing?: Drawing;
    selectedDrawings?: Drawing[];
    clickPosition?: { x: number; y: number };
  }>({
    visible: false,
    x: 0,
    y: 0,
    type: 'canvas',
  });

  // Handle context menu for drawings
  const handleDrawingContextMenu = (
    drawing: Drawing,
    e: Konva.KonvaEventObject<PointerEvent>,
  ) => {
    e.evt.preventDefault();

    // In view mode, don't show context menus for drawings
    if (!isEditMode) {
      return;
    }

    const stage = e.target.getStage();
    if (!stage) return;

    const containerRect = stage.container().getBoundingClientRect();
    const pointerPosition = stage.getPointerPosition();
    if (!pointerPosition) return;

    // Check if we have multiple drawings selected
    if (selectedDrawingIds.length > 1) {
      const selectedDrawings = fetchedDrawings.filter((d) =>
        selectedDrawingIds.includes(d.id),
      );
      setContextMenu({
        visible: true,
        x: containerRect.left + pointerPosition.x,
        y: containerRect.top + pointerPosition.y,
        type: 'multiselection',
        selectedDrawings,
      });
    } else {
      setContextMenu({
        visible: true,
        x: containerRect.left + pointerPosition.x,
        y: containerRect.top + pointerPosition.y,
        type: 'drawing',
        drawing,
      });
    }
  };

  // Handle context menu for canvas (ImageLayer)
  const handleCanvasContextMenu = (e: Konva.KonvaEventObject<PointerEvent>) => {
    e.evt.preventDefault();

    // In view mode, don't show context menus for canvas
    if (!isEditMode) {
      return;
    }

    const stage = e.target.getStage();
    if (!stage) return;

    const containerRect = stage.container().getBoundingClientRect();
    const pointerPosition = stage.getPointerPosition();
    if (!pointerPosition) return;

    // Get relative position for pasting
    const relativePosition = {
      x: (pointerPosition.x - position.x) / zoom,
      y: (pointerPosition.y - position.y) / zoom,
    };

    setContextMenu({
      visible: true,
      x: containerRect.left + pointerPosition.x,
      y: containerRect.top + pointerPosition.y,
      type: 'canvas',
      clickPosition: relativePosition,
    });
  };

  // Handle context menu for selection bounds
  const handleSelectionBoundsContextMenu = (
    e: any,
    _bounds: { x: number; y: number; width: number; height: number },
  ) => {
    e.evt.preventDefault();

    // In view mode, don't show context menus for selection bounds
    if (!isEditMode) {
      return;
    }

    const stage = e.target.getStage();
    if (!stage) return;

    const containerRect = stage.container().getBoundingClientRect();
    const pointerPosition = stage.getPointerPosition();
    if (!pointerPosition) return;

    // Get selected drawings for the context menu
    const selectedDrawings = fetchedDrawings.filter((d) =>
      selectedDrawingIds.includes(d.id),
    );

    setContextMenu({
      visible: true,
      x: containerRect.left + pointerPosition.x,
      y: containerRect.top + pointerPosition.y,
      type: 'multiselection',
      selectedDrawings,
    });
  };

  // Handle paste operation
  const handlePaste = (_x: number, _y: number) => {
    setContextMenu((prev) => ({ ...prev, visible: false }));
  };

  // Close context menu when clicking elsewhere
  const handleStageContextMenu = (e: Konva.KonvaEventObject<PointerEvent>) => {
    // Only prevent default if right-clicking on the stage itself (not on drawings or image)
    if (e.target === e.target.getStage()) {
      e.evt.preventDefault();
      setContextMenu((prev) => ({ ...prev, visible: false }));
    }
  };

  // Handle drawing hover for tooltip
  const handleDrawingHover = (
    drawing: Drawing,
    e: Konva.KonvaEventObject<MouseEvent>,
  ) => {
    // Only show tooltip in view mode (!isEditMode)
    if (isEditMode) return;

    const stage = e.target.getStage();
    if (!stage) return;

    const containerRect = stage.container().getBoundingClientRect();
    const pointerPosition = stage.getPointerPosition();
    if (!pointerPosition) return;

    setTooltip({
      visible: true,
      position: {
        x: containerRect.left + pointerPosition.x,
        y: containerRect.top + pointerPosition.y,
      },
      drawing,
    });
  };

  // Handle drawing mouse move for tooltip position updates
  const handleDrawingMouseMove = (
    drawing: Drawing,
    e: Konva.KonvaEventObject<MouseEvent>,
  ) => {
    // Only show tooltip in view mode (!isEditMode)
    if (isEditMode) return;

    // Only update position if tooltip is visible and for the same drawing
    if (!tooltip.visible || tooltip.drawing?.id !== drawing.id) return;

    const stage = e.target.getStage();
    if (!stage) return;

    const containerRect = stage.container().getBoundingClientRect();
    const pointerPosition = stage.getPointerPosition();
    if (!pointerPosition) return;

    setTooltip({
      ...tooltip,
      position: {
        x: containerRect.left + pointerPosition.x,
        y: containerRect.top + pointerPosition.y,
      },
    });
  };

  // Handle drawing hover end for tooltip
  const handleDrawingHoverEnd = () => {
    setTooltip({
      visible: false,
      position: { x: 0, y: 0 },
      drawing: null,
    });
  };

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (_event: MouseEvent) => {
      if (contextMenu.visible) {
        setContextMenu((prev) => ({ ...prev, visible: false }));
      }
    };

    if (contextMenu.visible) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [contextMenu.visible]);

  return (
    <>
      <Stage
        width={dimensions.width}
        height={dimensions.height}
        scaleX={zoom}
        scaleY={zoom}
        x={position.x}
        y={position.y}
        ref={stageRef}
        className={getCursorClass()}
        onMouseDown={(e) => {
          // Don't handle mouse events if context menu is visible
          if (contextMenu.visible) {
            return;
          }
          handleStageClick(e); // Handle deselection first
          handleMouseDown(e); // Then handle drawing
        }}
        onMouseMove={(e) => {
          // Don't handle mouse events if context menu is visible
          if (contextMenu.visible) {
            return;
          }

          // Track mouse position for keyboard paste
          const stage = e.target.getStage();
          if (stage) {
            const relativePos = getRelativePointerPosition(
              stage,
              position,
              zoom,
            );
            mousePositionRef.current = relativePos;
          }

          handleMouseMove(e);
        }}
        onMouseUp={(e) => {
          // Don't handle mouse events if context menu is visible
          if (contextMenu.visible) {
            return;
          }
          handleMouseUp(e);
        }}
        onContextMenu={handleStageContextMenu}
        draggable={isPanMode && !isShapeDragging && !isGroupDragging}
        onDragStart={() => {
          setIsCanvasDragging(true);
        }}
        onDragEnd={(e) => {
          setIsCanvasDragging(false);
          if (isPanMode && !isShapeDragging && !isGroupDragging) {
            setPosition({ x: e.target.x(), y: e.target.y() });
            // Trigger bounce-back if panning went beyond boundaries
            triggerBounceBack?.();
          }
        }}
      >
        {/* Layer for the blueprint image */}
        <ImageLayer
          selectedImagePath={selectedImagePath}
          dimensions={imageLayerDimensions}
          handleStageClick={handleStageClick}
          handleCanvasContextMenu={handleCanvasContextMenu}
        />

        {/* Layer for drawings */}
        <DrawingLayer
          fetchedDrawings={fetchedDrawings}
          drawingsError={drawingsError}
          currentDrawing={currentDrawing}
          dimensions={imageLayerDimensions}
          zoom={zoom}
          selectedTool={selectedTool}
          setIsShapeDragging={setIsShapeDragging}
          groupDragOffset={groupDragOffset}
          handleDragEnd={handleDragEnd}
          handleGroupDragStart={handleGroupDragStart}
          handleGroupDragMove={handleGroupDragMove}
          handleGroupDragEnd={handleGroupDragEnd}
          handleDrawingSelect={handleDrawingSelect}
          handleDrawingContextMenu={handleDrawingContextMenu}
          handleSelectionBoundsContextMenu={handleSelectionBoundsContextMenu}
          handleDrawingHover={handleDrawingHover}
          handleDrawingMouseMove={handleDrawingMouseMove}
          handleDrawingHoverEnd={handleDrawingHoverEnd}
        />
      </Stage>

      {/* Context Menus */}
      {contextMenu.visible && (
        <div
          style={{
            position: 'fixed',
            top: contextMenu.y,
            left: contextMenu.x,
            zIndex: 1000,
            backgroundColor: 'white',
            border: '1px solid #ccc',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            minWidth: '120px',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {contextMenu.type === 'drawing' && contextMenu.drawing && (
            <DrawingContextMenu
              drawing={contextMenu.drawing}
              onClose={() =>
                setContextMenu((prev) => ({ ...prev, visible: false }))
              }
            />
          )}
          {contextMenu.type === 'multiselection' &&
            contextMenu.selectedDrawings && (
              <MultiSelectionContextMenu
                selectedDrawings={contextMenu.selectedDrawings}
                onClose={() =>
                  setContextMenu((prev) => ({ ...prev, visible: false }))
                }
              />
            )}
          {contextMenu.type === 'canvas' && contextMenu.clickPosition && (
            <CanvasContextMenu
              onPaste={handlePaste}
              onClose={() =>
                setContextMenu((prev) => ({ ...prev, visible: false }))
              }
              clickPosition={contextMenu.clickPosition}
              dimensions={imageLayerDimensions}
            />
          )}
        </div>
      )}

      {/* Custom Cursor */}
      <CustomCursor
        selectedTool={selectedTool}
        isSpacebarPanning={isSpacebarPanning}
        isMiddleMousePanning={isMiddleMousePanning}
        isDragging={cursorState.isDragging || isCanvasDragging}
        isHoveringDrawing={cursorState.isHoveringDrawing}
        isDrawing={cursorState.isDrawing}
      />

      {/* Drawing Tooltip */}
      <DrawingTooltip
        visible={tooltip.visible}
        position={tooltip.position}
        drawing={tooltip.drawing}
        scale={selectedImage?.scale || null}
        paperSize={paperSize}
      />
    </>
  );
}
