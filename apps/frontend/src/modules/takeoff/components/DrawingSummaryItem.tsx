import React from 'react';
import { DrawingSummaryItem as DrawingSummaryItemType } from '@repo/component-summary';
import { GeometryType } from '../types/component';

interface DrawingSummaryItemProps {
  drawing: DrawingSummaryItemType;
  componentType: GeometryType;
}

export const DrawingSummaryItemComponent: React.FC<DrawingSummaryItemProps> = ({
  drawing,
  componentType,
}) => {
  return (
    <div className="flex justify-between items-center py-1 px-2 rounded hover:bg-muted/30 transition-colors">
      <span className="text-xs font-mono">{drawing.displayId}</span>
      <span className="text-xs">
        {drawing.measurement
          ? `${drawing.measurement.formattedValue} ${drawing.measurement.unit}`
          : componentType === 'point'
            ? '1 point'
            : 'N/A'}
      </span>
    </div>
  );
};
