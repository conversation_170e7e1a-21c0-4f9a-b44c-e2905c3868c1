import { Drawing } from '../types/drawing';
import { ScaleInfo, PaperSize } from '@repo/component-summary';
import {
  MeasurementBadge,
  calculateMeasurementPositionFromConfig,
  checkMinimumSizeForConfig,
  calculateMeasurementData,
} from '../utils/measurement-utils';

interface PersistentMeasurementProps {
  drawing: Drawing;
  scale: ScaleInfo;
  paperSize: PaperSize;
  zoom: number; // The zoom scale of the canvas
}

/**
 * Component to display persistent measurements for completed measurement drawings
 */
export function PersistentMeasurement({
  drawing,
  scale,
  paperSize,
  zoom,
}: PersistentMeasurementProps) {
  // Only show for measurement drawings (drawings without a component)
  if (drawing.componentId !== null) {
    return null;
  }

  const config = drawing.config;

  // Check minimum size requirements for different shape types
  if (!checkMinimumSizeForConfig(config)) {
    return null;
  }

  // Calculate measurements using the dynamic measurements utility
  const { measurements, displayText } = calculateMeasurementData(
    config,
    scale,
    paperSize,
  );

  if (!measurements || !displayText) {
    return null;
  }

  // Calculate the position for the measurement badge
  const position = calculateMeasurementPositionFromConfig(config, zoom);

  return (
    <MeasurementBadge
      text={displayText}
      x={position.x}
      y={position.y}
      zoom={zoom}
    />
  );
}
