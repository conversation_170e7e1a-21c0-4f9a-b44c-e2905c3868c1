'use client';

import { useRef, useState, useEffect } from 'react';
import { useGetDrawings } from '../api/queries';
import { useTakeoffStore } from '../store/takeoff-store';

// Import custom hooks
import { useCanvasDimensions } from '../hooks/useCanvasDimensions';
import { useCanvasZoom } from '../hooks/useCanvasZoom';
import { useDrawingTools } from '../hooks/useDrawingTools';
import { useToolKeyboardShortcuts } from '../hooks/useToolKeyboardShortcuts';
import { REFERENCE_CANVAS_DIMENSIONS } from '@repo/component-summary';
import {
  getFullScreenCanvasDimensions,
  TOP_TOOLBAR_SPACE,
  BOTTOM_TOOLBAR_SPACE,
} from '../constants/layout';

// Import components
import { CanvasToolbar } from './CanvasToolbar';
import { CanvasStage } from './CanvasStage';
import { DrawingToolbar } from './DrawingToolbar';
import { CommentForm } from './CommentForm';

interface CanvasAreaProps {
  takeoffId: string;
}

export function CanvasArea({ takeoffId }: CanvasAreaProps) {
  // Create refs
  const containerRef = useRef<HTMLDivElement | null>(null);
  const mousePositionRef = useRef<{ x: number; y: number } | null>(null);

  // Get store data
  const {
    selectedImage: storeSelectedImage,
    isEditMode,
    setCanvasState,
  } = useTakeoffStore();
  const currentBlueprintImageId = storeSelectedImage?.id;

  // Use fixed canvas dimensions instead of responsive ones
  // This ensures the canvas and image always have the same size regardless of viewport
  const dimensions = REFERENCE_CANVAS_DIMENSIONS;
  const { position, setPosition } = useCanvasDimensions({
    containerRef,
    initialDimensions: dimensions,
    useFixedDimensions: true,
  });

  // Calculate full screen canvas dimensions (minus toolbars) with responsive updates
  const [fullScreenDimensions, setFullScreenDimensions] = useState(
    getFullScreenCanvasDimensions(),
  );

  const {
    zoom,
    handleWheel,
    handleGestureChange,
    resetTouchTracking,
    zoomIn,
    zoomOut,
    resetZoom,
    zoomPercentage,
    triggerBounceBack,
  } = useCanvasZoom({
    setPosition,
    containerRef, // Pass the container ref to handle gesture events
    mousePositionRef, // Pass the mouse position ref for keyboard zoom
    imageDimensions: dimensions, // Pass image dimensions for constraints
    viewportDimensions: fullScreenDimensions, // Pass viewport dimensions for constraints
  });

  // Fetch drawings from API
  const { data: fetchedDrawings, error: drawingsError } = useGetDrawings(
    { blueprintImageId: currentBlueprintImageId || '' },
    { enabled: !!currentBlueprintImageId },
  );

  const {
    selectedTool,
    setSelectedTool,
    currentDrawing,
    isShapeDragging,
    setIsShapeDragging,
    groupDragOffset,
    isSpacebarPanning,
    setIsSpacebarPanning,
    isMiddleMousePanning,
    showCommentForm,
    commentFormPosition,
    handleCommentSave,
    handleCommentCancel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleDragEnd,
    handleGroupDragStart,
    handleGroupDragMove,
    handleGroupDragEnd,
    handleDrawingSelect,
    handleStageClick,
    cancelCurrentDrawing,
  } = useDrawingTools({
    zoom, // number for zoom/transform
    position,
    setPosition,
    fetchedDrawings,
    dimensions,
    viewportDimensions: fullScreenDimensions,
  });

  // Add keyboard shortcuts for tool switching
  useToolKeyboardShortcuts({
    setSelectedTool,
    selectedTool,
    setIsSpacebarPanning,
    dimensions,
    cancelCurrentDrawing,
    currentDrawing,
    mousePositionRef,
  });

  // Force pan tool when switching to view mode
  useEffect(() => {
    if (!isEditMode && selectedTool !== 'pan') {
      setSelectedTool('pan');
    }
  }, [isEditMode, selectedTool, setSelectedTool]);

  // Update canvas state in store for coordinate transformations
  useEffect(() => {
    setCanvasState({
      zoom,
      position,
    });
  }, [zoom, position, setCanvasState]);

  // Gesture events are now handled in the useCanvasZoom hook

  // Set initial canvas dimensions
  useEffect(() => {
    if (containerRef.current) {
      setFullScreenDimensions({
        width: containerRef.current.clientWidth,
        height: containerRef.current.clientHeight,
      });
    }
  }, []);

  // Center the image on initial render and when canvas dimensions change
  useEffect(() => {
    const centerX = (fullScreenDimensions.width - dimensions.width) / 2;
    const centerY = (fullScreenDimensions.height - dimensions.height) / 2;
    setPosition({ x: centerX, y: centerY });
  }, [
    fullScreenDimensions.width,
    fullScreenDimensions.height,
    dimensions.width,
    dimensions.height,
    setPosition,
  ]);

  return (
    <div
      className="relative h-screen w-screen overflow-hidden canvas-container z-0"
      style={{ isolation: 'isolate' }}
    >
      {/* Top toolbar */}
      <CanvasToolbar
        zoomIn={zoomIn}
        zoomOut={zoomOut}
        resetZoom={resetZoom}
        zoomPercentage={zoomPercentage}
        takeoffId={takeoffId}
      />

      {/* Full-screen canvas content */}
      <div
        ref={containerRef}
        className="absolute overflow-hidden bg-[#f5f5f5]"
        style={{
          top: TOP_TOOLBAR_SPACE,
          bottom: BOTTOM_TOOLBAR_SPACE,
          left: 0,
          right: 0,
        }}
        onWheel={handleWheel}
        onTouchStart={(e) => e.touches.length > 1 && e.preventDefault()}
        onTouchMove={handleGestureChange}
        onTouchEnd={resetTouchTracking}
      >
        {/*iFull-screen canvas stage with fixed image dimensions */}
        <CanvasStage
          dimensions={fullScreenDimensions} // Full screen for canvas
          imageDimensions={dimensions} // Fixed dimensions for image
          zoom={zoom}
          position={position}
          setPosition={setPosition}
          selectedTool={selectedTool}
          isShapeDragging={isShapeDragging}
          isSpacebarPanning={isSpacebarPanning}
          isMiddleMousePanning={isMiddleMousePanning}
          currentDrawing={currentDrawing}
          selectedImagePath={storeSelectedImage?.path}
          fetchedDrawings={fetchedDrawings}
          drawingsError={drawingsError}
          setIsShapeDragging={setIsShapeDragging}
          handleMouseDown={handleMouseDown}
          handleMouseMove={handleMouseMove}
          handleMouseUp={handleMouseUp}
          handleDragEnd={handleDragEnd}
          handleGroupDragStart={handleGroupDragStart}
          handleGroupDragMove={handleGroupDragMove}
          handleGroupDragEnd={handleGroupDragEnd}
          groupDragOffset={groupDragOffset}
          handleDrawingSelect={handleDrawingSelect}
          handleStageClick={handleStageClick}
          mousePositionRef={mousePositionRef}
          triggerBounceBack={triggerBounceBack}
        />

        {/* Comment Form */}
        <CommentForm
          x={commentFormPosition.x}
          y={commentFormPosition.y}
          onSave={handleCommentSave}
          onCancel={handleCommentCancel}
          isVisible={showCommentForm}
        />
      </div>

      {/* Bottom toolbar */}
      <DrawingToolbar
        selectedTool={selectedTool}
        onToolChange={setSelectedTool}
      />
    </div>
  );
}
