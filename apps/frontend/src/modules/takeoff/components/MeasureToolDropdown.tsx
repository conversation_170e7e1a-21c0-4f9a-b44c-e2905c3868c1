import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { ChevronDown } from 'lucide-react';
import { MeasureToolVariant } from '../store/takeoff-store';
import { measureToolOptions } from '../constants/measure-tools';
import { SelectedTool } from '../types/drawing-types';

interface MeasureToolDropdownProps {
  selectedMeasureTool: MeasureToolVariant;
  onMeasureToolChange: (tool: MeasureToolVariant) => void;
  selectedTool: SelectedTool;
  disabled?: boolean;
}

export function MeasureToolDropdown({
  selectedMeasureTool,
  onMeasureToolChange,
  selectedTool,
  disabled,
}: MeasureToolDropdownProps) {
  const selectedOption =
    measureToolOptions.find((option) => option.id === selectedMeasureTool) ||
    measureToolOptions[0];

  // Check if any measure tool is currently selected
  const isMeasureToolSelected = measureToolOptions.some(
    (option) => option.baseTool === selectedTool,
  );

  return (
    <DropdownMenu>
      <Tooltip>
        <TooltipTrigger asChild>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              disabled={disabled}
              className={`w-12 h-10 p-0 rounded-lg transition-colors flex items-center justify-center gap-0.5 ${
                isMeasureToolSelected
                  ? '!bg-primary !text-primary-foreground hover:!bg-primary/90'
                  : 'hover:bg-muted'
              }`}
            >
              <selectedOption.icon className="h-5 w-5" />
              <ChevronDown
                className={`h-3 w-3 ${isMeasureToolSelected ? 'opacity-75' : 'opacity-50'}`}
              />
            </Button>
          </DropdownMenuTrigger>
        </TooltipTrigger>
        <TooltipContent>
          <p>{selectedOption.label}</p>
        </TooltipContent>
      </Tooltip>
      <DropdownMenuContent className="w-[280px] p-2">
        {measureToolOptions.map((option) => (
          <DropdownMenuItem
            key={option.id}
            onSelect={() => onMeasureToolChange(option.id)}
            className="rounded-lg p-3 cursor-pointer hover:bg-muted/50 transition-colors"
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <option.icon className="h-5 w-5 mr-3" />
                <span className="font-medium">{option.label}</span>
              </div>
              <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md">
                {option.isSurface ? 'Area' : 'Line'}
              </span>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
