'use client';

import Image from 'next/image';
import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Pencil } from 'lucide-react';

import { useBlueprintImages } from '../api/queries';
import { useUpdateImageScaleAndDimensions } from '../api/mutations'; // Updated import
import { SidebarMenuItem } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import {
  useTakeoffStore,
  ImageType as StoreImageType,
  BlueprintFileItemType as StoreBlueprintFileItemType,
} from '../store/takeoff-store';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { EditScaleModal } from './EditScaleModal'; // Import the new modal
import { queryKeys } from '@/lib/query-keys'; // For query invalidation
import { toast } from '@/lib/toast'; // For notifications
import { DEFAULT_PAPER_SIZE } from '@repo/component-summary';

// Use types from the store for consistency
type PdfFileType = StoreBlueprintFileItemType;
type ImageType = StoreImageType;
type ScaleType = NonNullable<ImageType['scale']>;
type DimensionsType = NonNullable<ImageType['dimensions']>; // Added dimensions type

interface PageItemProps {
  fileData: PdfFileType; // The parent blueprint file data
}

export function PageItem({ fileData }: PageItemProps) {
  const { data: imagesResponse, isLoading } = useBlueprintImages(fileData.id);
  const { selectedImage, setSelectedImage, isEditMode } = useTakeoffStore();

  const [isEditScaleModalOpen, setIsEditScaleModalOpen] = useState(false);
  const [currentImageForScaleEdit, setCurrentImageForScaleEdit] =
    useState<ImageType | null>(null);
  const [currentPageNumberForModal, setCurrentPageNumberForModal] =
    useState<number>(0);

  const queryClient = useQueryClient();
  // Updated to use the new mutation hook
  const { mutateAsync: updateImageScaleAndDimensions, isPending: isUpdating } =
    useUpdateImageScaleAndDimensions();
  const { setSelectedImage: setStoreSelectedImage } = useTakeoffStore();

  const handleSave = async (
    imageId: string,
    newScale: ScaleType,
    newDimensions: DimensionsType,
  ) => {
    if (!currentImageForScaleEdit) {
      toast.error('No image selected for update.');
      return;
    }
    try {
      await updateImageScaleAndDimensions({
        imageId,
        scale: newScale,
        dimensions: newDimensions,
      });
      toast.success('Scale and dimensions updated successfully!');

      // Optimistically update the store
      const updatedImage = {
        ...currentImageForScaleEdit,
        scale: newScale,
        dimensions: newDimensions,
      };
      setStoreSelectedImage(updatedImage as ImageType); // Update the store

      await queryClient.invalidateQueries({
        queryKey: queryKeys.takeoff.blueprintImages(fileData.id),
      });

      setIsEditScaleModalOpen(false);
      setCurrentImageForScaleEdit(null);
    } catch (error) {
      console.error('Failed to update scale and dimensions:', error);
      toast.error('Failed to update. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <SidebarMenuItem className="py-0 px-2">
        <div className="py-2">
          <Skeleton className="h-8 w-3/4 mb-2" />
          <Skeleton className="h-20 w-full" />
        </div>
      </SidebarMenuItem>
    );
  }

  return (
    <SidebarMenuItem className="py-0">
      {imagesResponse?.data?.map((image, index) => (
        <div
          key={image.id}
          className={`mb-1 relative group hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer ${
            selectedImage?.id === image.id
              ? 'bg-gray-200 dark:bg-gray-600 font-semibold'
              : ''
          }`}
          onClick={() => setSelectedImage(image as ImageType)}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ')
              setSelectedImage(image as ImageType);
          }}
        >
          <div className="flex items-start p-2 gap-3">
            <div className="min-w-[80px] w-[80px] h-[80px] flex-shrink-0">
              {image.path && (
                <Image
                  src={image.path}
                  alt={image.filename || `Page ${index + 1}`}
                  width={80}
                  height={80}
                  className="border object-cover w-full h-full"
                />
              )}
            </div>
            <div className="flex flex-col justify-center min-w-0 flex-grow">
              <div className="text-sm truncate">
                Page {index + 1}. : {image.filename || 'Untitled Sheet'}
              </div>
              <div className="flex items-start gap-1 text-xs text-muted-foreground mt-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-5 w-5 flex-shrink-0 cursor-pointer ${
                    isEditMode ? 'cursor-pointer' : 'opacity-50'
                  }`} // Added flex-shrink-0
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent triggering onClick of parent div
                    if (!isEditMode) {
                      toast.error(
                        'Edit mode required to change scale and dimensions',
                      );
                      return;
                    }
                    setCurrentImageForScaleEdit(image as ImageType);
                    setCurrentPageNumberForModal(index + 1); // Set page number for modal title
                    setIsEditScaleModalOpen(true);
                  }}
                  aria-label={
                    isEditMode
                      ? 'Edit scale and dimensions'
                      : 'Edit scale and dimensions (Edit mode required)'
                  }
                  title={
                    isEditMode
                      ? 'Edit scale and dimensions'
                      : 'Edit mode required'
                  }
                >
                  <Pencil className="h-3 w-3" />{' '}
                  {/* Increased icon size for better visibility */}
                </Button>
                <div className="flex flex-col">
                  {image.scale ? (
                    <span>
                      Scale: {image.scale.num_metric} {image.scale.num_unit} :{' '}
                      {image.scale.den_metric.toFixed(2)} {image.scale.den_unit}
                    </span>
                  ) : (
                    <span className="italic">Scale not set</span>
                  )}
                  {image.dimensions ? (
                    <span className="mt-0.5">
                      Dimensions: {image.dimensions.width}" x{' '}
                      {image.dimensions.height}"
                    </span>
                  ) : (
                    <span className="mt-0.5">
                      Dimensions: {DEFAULT_PAPER_SIZE.width}" x{' '}
                      {DEFAULT_PAPER_SIZE.height}"
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
          {index < imagesResponse.data.length - 1 && (
            <Separator className="my-1 mx-2" />
          )}
        </div>
      ))}
      {(!imagesResponse?.data || imagesResponse.data.length === 0) &&
        !isLoading && (
          <div className="p-2 text-xs text-muted-foreground">
            No images found for this file.
          </div>
        )}

      {currentImageForScaleEdit && (
        <EditScaleModal
          isOpen={isEditScaleModalOpen}
          onClose={() => {
            setIsEditScaleModalOpen(false);
            setCurrentImageForScaleEdit(null); // Clear image when closing
          }}
          image={currentImageForScaleEdit}
          pageNumber={currentPageNumberForModal}
          onSave={handleSave} // Updated to handleSave
          isSaving={isUpdating} // Updated to isUpdating
        />
      )}
    </SidebarMenuItem>
  );
}
