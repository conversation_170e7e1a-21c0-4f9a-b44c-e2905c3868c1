import React from 'react';
import { Label, Tag, Text } from 'react-konva';

interface CommentAnnotationProps {
  x: number;
  y: number;
  text: string;
  id: number;
  onSelect?: (id: number) => void;
  onDelete?: (id: number) => void;
  isSelected?: boolean;
  onClick?: (id: number) => void;
  onContextMenu?: (e: any) => void;
}

/**
 * Comment annotation component for React Konva canvas
 * Renders a comment bubble with text content at specified coordinates
 */
export const CommentAnnotation: React.FC<CommentAnnotationProps> = ({
  x,
  y,
  text,
  id,
  onSelect,
  onDelete,
  isSelected = false,
  onClick,
  onContextMenu,
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(id);
    }
    if (onSelect) {
      onSelect(id);
    }
  };

  const handleContextMenu = (e: any) => {
    e.evt.preventDefault();
    if (onContextMenu) {
      onContextMenu(e);
    } else if (onDelete) {
      // Fallback to simple delete if no context menu handler provided
      onDelete(id);
    }
  };

  return (
    <Label
      x={x}
      y={y}
      shadowColor="rgba(0, 0, 0, 0.15)"
      shadowBlur={8}
      shadowOffsetX={0}
      shadowOffsetY={2}
      shadowOpacity={1}
      onClick={handleClick}
      onContextMenu={handleContextMenu}
      listening={true}
    >
      <Tag
        fill="#FFFFFF"
        cornerRadius={[8, 8, 8, 2]}
        stroke={isSelected ? '#007bff' : 'rgba(0, 0, 0, 0.25)'}
        strokeWidth={isSelected ? 2 : 1}
      />
      <Text
        text={text}
        fontFamily="Inter, -apple-system, sans-serif"
        fontSize={14}
        padding={12}
        fill="#333333"
        width={200}
        wrap="word"
        align="left"
      />
    </Label>
  );
};
