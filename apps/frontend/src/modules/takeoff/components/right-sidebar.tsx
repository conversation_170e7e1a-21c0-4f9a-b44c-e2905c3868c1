'use client';

import { ChevronDown, PanelRightOpen, Search } from 'lucide-react'; // Added PanelRightOpen
import type React from 'react';
import { useState } from 'react';
import { PageItem } from './page-item';

import { Button } from '@/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  useSidebar, // Added useSidebar
} from '@/components/ui/sidebar';
import {
  BlueprintFileItemType as StoreBlueprintFileItemType,
  useTakeoffStore,
} from '../store/takeoff-store';

// Use types from the store for consistency
type PdfFileType = StoreBlueprintFileItemType;

interface RightSidebarProps {
  files: PdfFileType[] | null;
  onSelectFile: (file: PdfFileType) => void;
}

export function RightSidebar({ files, onSelectFile }: RightSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { selectedFile } = useTakeoffStore();
  const { toggleRightSidebar } = useSidebar(); // Added

  // TODO: Implement actual search filtering if needed
  const filteredFiles = files?.filter((file) =>
    file.fileName.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <Sidebar
      side="right"
      className="border-l"
      style={{ '--sidebar-width': '22rem' } as React.CSSProperties}
    >
      <SidebarHeader className="h-14 border-b">
        <div className="flex items-center justify-between px-2">
          {' '}
          {/* Adjusted padding to px-2 like left sidebars */}
          <h2 className="text-sm font-medium">Files</h2>
          <div className="flex items-center gap-1">
            {' '}
            {/* Added a div to group buttons */}
            <Button
              onClick={toggleRightSidebar}
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              aria-label="Toggle sidebar"
            >
              <PanelRightOpen className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <div className="flex items-center justify-between px-4 py-2">
            <span className="text-sm text-muted-foreground">
              Total sheets: {filteredFiles?.length ?? 0}
            </span>
            <div className="flex items-center gap-2">
              {/* Placeholder for "Show only workable sheets" functionality if needed */}
            </div>
          </div>

          <div className="px-4 py-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search sheets by name..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {filteredFiles?.map((file, index) => (
            <Collapsible
              key={file.id}
              defaultOpen={
                selectedFile?.id === file.id || (!selectedFile && index === 0)
              }
              className="px-2 py-1"
            >
              <CollapsibleTrigger
                className={`flex w-full items-center justify-between rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 px-2 py-2 text-sm ${
                  selectedFile?.id === file.id ? 'bg-muted' : ''
                }`}
                onClick={() => onSelectFile(file)} // Select file on trigger click
              >
                <div className="flex items-center gap-2 min-w-0">
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                  <span className="font-medium truncate" title={file.fileName}>
                    {file.fileName}
                  </span>
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="pl-2">
                {' '}
                {/* Indent content under trigger */}
                <SidebarGroupContent>
                  <SidebarMenu>
                    <PageItem key={file.id} fileData={file} />
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </Collapsible>
          ))}
          {(!filteredFiles || filteredFiles.length === 0) && (
            <div className="px-4 py-2 text-sm text-muted-foreground">
              {searchQuery
                ? 'No files match your search.'
                : 'No files available.'}
            </div>
          )}
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
