'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useSidebar } from '@/components/ui/sidebar';
import { PanelRightOpen } from 'lucide-react';
import React from 'react';

interface RightSidebarHeaderProps {
  title?: string;
}

export function RightSidebarHeader({
  title = 'Files',
}: RightSidebarHeaderProps) {
  const { toggleRightSidebar } = useSidebar();

  return (
    <div className="fixed top-4 right-4 z-20 flex h-12 items-center gap-1 bg-background border border-border rounded-full px-2 shadow-lg w-fit">
      {/* Toggle Sidebar Button */}
      <Button
        onClick={toggleRightSidebar}
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        aria-label="Expand Files Sidebar"
        title="Expand Files Sidebar"
      >
        <PanelRightOpen className="h-4 w-4" />
      </Button>

      {/* Separator */}
      <div className="w-px h-6 bg-border mx-1" />

      {/* Title */}
      <span className="text-sm font-medium px-2 text-foreground">{title}</span>
    </div>
  );
}
