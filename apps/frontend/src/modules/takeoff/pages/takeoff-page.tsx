'use client';

import React, { useEffect } from 'react'; // Added useEffect
import { SidebarProvider, useSidebar } from '@/components/ui/sidebar';
import { Spinner } from '@/components/ui/spinner';
import { useParams } from 'next/navigation';
import { useBlueprintFiles, useBlueprintImages } from '../api/queries'; // Added useBlueprintImages
import { CanvasArea } from '../components/canvas-area';
import { LeftSidebar } from '../components/left-sidebar';
import { LeftSidebarHeader } from '../components/LeftSidebarHeader';
import { RightSidebar } from '../components/right-sidebar';
import { RightSidebarHeader } from '../components/RightSidebarHeader';
import {
  useTakeoffStore,
  BlueprintFileItemType,
  ImageType as StoreImageType,
} from '../store/takeoff-store';
import { BlueprintFilesResponse } from '../types/blueprint-files';

// Component that uses the sidebar context
const TakeoffContent = ({
  takeoffId,
  blueprintFilesResponse,
  setSelectedFile,
}: {
  takeoffId: string;
  blueprintFilesResponse: BlueprintFilesResponse | undefined;
  setSelectedFile: (file: BlueprintFileItemType) => void;
}) => {
  const { leftOpen, rightOpen } = useSidebar();

  return (
    <>
      {/* Full-screen canvas area */}
      <CanvasArea takeoffId={takeoffId} />

      {/* Absolutely positioned sidebars */}
      <div className="absolute top-0 left-0 z-10">
        {leftOpen ? (
          <LeftSidebar
            projectName={blueprintFilesResponse?.data?.[0]?.takeoff?.name}
          />
        ) : (
          <LeftSidebarHeader
            projectName={blueprintFilesResponse?.data?.[0]?.takeoff?.name}
          />
        )}
      </div>
      <div className="absolute top-0 right-0 z-10">
        {rightOpen ? (
          <RightSidebar
            files={blueprintFilesResponse?.data ?? null}
            onSelectFile={setSelectedFile}
          />
        ) : (
          <RightSidebarHeader title="Files" />
        )}
      </div>
    </>
  );
};

export const TakeoffPage = () => {
  const params = useParams();
  const takeoffId = params.slug as string;

  const { data: blueprintFilesResponse, isLoading: isLoadingFiles } =
    useBlueprintFiles(takeoffId);

  const { selectedImage, selectedFile, setSelectedFile, setSelectedImage } =
    useTakeoffStore((state) => state);

  // Effect to select the first blueprint file by default
  useEffect(() => {
    if (
      blueprintFilesResponse?.data &&
      blueprintFilesResponse.data.length > 0 &&
      !selectedFile
    ) {
      const firstFile = blueprintFilesResponse.data[0] as BlueprintFileItemType;
      setSelectedFile(firstFile);
      // Note: Image selection will be handled by the effect below once selectedFile is set
    }
  }, [blueprintFilesResponse, selectedFile, setSelectedFile]);

  const { data: imagesForSelectedFile, isLoading: isLoadingImages } =
    useBlueprintImages(selectedFile?.id);

  // Effect to select the first image of the selected file if no image is selected
  useEffect(() => {
    if (
      selectedFile &&
      imagesForSelectedFile?.data &&
      imagesForSelectedFile.data.length > 0 &&
      !selectedImage
    ) {
      setSelectedImage(imagesForSelectedFile.data[0] as StoreImageType);
    }
  }, [
    selectedFile,
    imagesForSelectedFile,
    selectedImage,
    setSelectedImage,
    isLoadingImages,
  ]);

  if (isLoadingFiles) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-background">
        <Spinner className="h-10 w-10" />
      </div>
    );
  }

  return (
    <div className="relative h-screen w-full overflow-hidden bg-[#f5f5f5]">
      <SidebarProvider>
        <TakeoffContent
          takeoffId={takeoffId}
          blueprintFilesResponse={blueprintFilesResponse}
          setSelectedFile={setSelectedFile}
        />
      </SidebarProvider>
    </div>
  );
};
