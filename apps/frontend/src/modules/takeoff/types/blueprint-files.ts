// Blueprint Files
import { DimensionInfo, ScaleInfo } from '@repo/component-summary';

export type BlueprintFilesResponse = {
  data: Datum[];
  meta: Meta;
};

interface Datum {
  id: string;
  takeoffId: number;
  fileName: string;
  fileUrl: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
  takeoff: Takeoff;
}

interface Takeoff {
  id: number;
  name: string;
  status: string;
}

// BluePrint File images
export interface BlueprintFile {
  data: Datum[];
  meta: Meta;
}

interface Datum {
  id: string;
  blueprintFileId: string;
  filename: string;
  path: string;
  awsKey: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
  scale: ScaleInfo;
  dimensions: DimensionInfo;
}

interface Meta {
  total: number;
  lastPage: number;
  currentPage: number;
  perPage: number;
  prev: null;
  next: null;
}
