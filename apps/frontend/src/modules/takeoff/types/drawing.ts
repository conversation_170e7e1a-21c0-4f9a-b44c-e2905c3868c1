// src/modules/takeoff/types/drawing.ts

// Simplified inline type for the 'takeoff' object nested within blueprintFile
interface TakeoffInfoForDrawing {
  id: number;
  userId: string;
  name: string;
}

// Simplified inline type for the 'blueprintFile' object nested within blueprintImage
interface BlueprintFileInfoForDrawing {
  id: string; // UUID
  fileName: string;
  takeoff: TakeoffInfoForDrawing;
}

// Simplified inline type for the 'blueprintImage' object nested within Drawing
interface BlueprintImageDetailsForDrawing {
  id: string; // UUID
  filename: string;
  blueprintFile: BlueprintFileInfoForDrawing;
}

// Simplified inline type for the 'component' object nested within Drawing
interface ComponentDetailsForDrawing {
  id: number;
  name: string;
  color: string;
  shade: string;
  geometricData?: Record<string, any>;
}

/**
 * Represents the data structure for a drawing object,
 * as returned by the GET /drawings endpoint.
 */
export interface Drawing {
  id: number; // Changed from string to number
  blueprintImageId: string;
  componentId: number | null; // Changed from string? to number | null
  config: Record<string, string>;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  blueprintImage?: BlueprintImageDetailsForDrawing; // Added, optional for robustness
  component?: ComponentDetailsForDrawing | null; // Added, optional for robustness
}

/**
 * Defines the payload structure for creating a new drawing
 * via the POST /drawings endpoint.
 */
export interface CreateDrawingPayload {
  blueprintImageId: string;
  componentId?: number; // Should be number to match the API expectation
  config: Record<string, string>;
  // Optional component data for optimistic updates (not sent to API)
  _optimisticComponent?: ComponentDetailsForDrawing | null;
  // Optional flag to skip optimistic updates (for undo/redo operations)
  skipOptimisticUpdate?: boolean;
}

/**
 * Defines the parameters for fetching drawings
 * via the GET /drawings endpoint.
 */
export interface GetDrawingsParams {
  blueprintImageId: string;
}
/**
 * Defines the payload structure for updating an existing drawing
 * via the PATCH /drawings/:drawingId endpoint.
 */
export interface UpdateDrawingPayload {
  drawingId: number; // To identify the drawing in the URL
  blueprintImageId: string; // Added for cache invalidation and optimistic updates
  componentId?: number; // Optional, as per PATCH body, API expects number
  config: Record<string, string>;
  // Optional flag to skip optimistic updates (for undo/redo operations)
  skipOptimisticUpdate?: boolean;
}

/**
 * Defines the response structure for updating a drawing.
 * Assumes the PATCH request returns the updated drawing, similar to create.
 */
export interface UpdateDrawingResponse {
  message: string;
  data: Drawing;
}
