export type GeometryType = 'surface' | 'edge' | 'point';
export type SelectionType = 'allPages' | 'currentPage';

export interface Component {
  id: number;
  name: string;
  selectionType: SelectionType;
  blueprintFileId: string;
  geometryType: GeometryType;
  blueprintImageId?: string; // Optional, present if selectionType is 'currentPage'
  description?: string; // Optional
  color: string;
  shade: string;
  geometricData?: Record<string, any>;
  // Add any other fields returned by GET /components if different
}

export interface CreateComponentPayload {
  name: string;
  selectionType: SelectionType;
  blueprintFileId: string;
  geometryType: GeometryType;
  blueprintImageId?: string;
  description?: string;
  color: string;
  shade: string;
  geometricData?: Record<string, any>;
}

// Assuming UpdateComponentPayload allows partial updates for flexibility,
// but the API might require all fields. Adjust if necessary.
export type UpdateComponentPayload = Partial<CreateComponentPayload> & {
  id: number;
};

export interface GetComponentsParams {
  blueprintFileId: string;
  blueprintImageId?: string; // As per user, this will always be passed
  activeTab: 'aggregate' | 'specific';
  search?: string;
}

// This type might be used by the Zustand store or other parts of the UI
export interface ImageType {
  id: string;
  name: string;
  url: string;
  // any other relevant properties for an image
}
