'use client';

import { Skeleton } from '@/components/ui/skeleton';

// Define column widths to match the main table
const columnWidths = [200, 120, 100, 100, 120, 180];

export function TakeoffTableLoading() {
  return (
    <div className="w-full">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200 bg-gray-50">
              {columnWidths.map((width, index) => (
                <th
                  key={index}
                  className="px-4 py-3 text-sm font-medium text-gray-500"
                  style={{
                    width,
                    textAlign: index === 5 ? 'center' : 'left',
                  }}
                >
                  <Skeleton className="h-4 w-24" />
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {Array.from({ length: 7 }).map((_, rowIndex) => (
              <tr key={rowIndex} className="bg-white">
                {columnWidths.map((width, colIndex) => {
                  if (colIndex === 5) {
                    // Actions column with 3 button skeletons
                    return (
                      <td
                        key={colIndex}
                        className="px-4 py-4 text-sm"
                        style={{
                          width,
                          textAlign: 'center',
                        }}
                      >
                        <div className="flex justify-center gap-1">
                          <Skeleton className="h-8 w-12 rounded-md border border-gray-200" />
                          <Skeleton className="h-8 w-12 rounded-md border border-gray-200" />
                          <Skeleton className="h-8 w-16 rounded-md border border-gray-200" />
                        </div>
                      </td>
                    );
                  }
                  return (
                    <td
                      key={colIndex}
                      className="px-4 py-4 text-sm"
                      style={{
                        width,
                        textAlign: 'left',
                      }}
                    >
                      <Skeleton
                        className={`h-4 ${colIndex === 0 ? 'w-32' : 'w-20'}`}
                      />
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
