export interface CreateTakeoffPayload {
  name: string;
  submissionDate: string;
  quotedPrice: number;
  dueDate: string;
  status: TakeoffStatus;
  files?: File[]; // Optional in the type since we'll handle files separately in FormData
}

export interface BlueprintFile {
  fileName: string;
  fileUrl: string;
}

export type TakeoffStatus =
  | 'submitted'
  | 'pending'
  | 'approved'
  | 'rejected'
  | 'completed';

export interface CreateTakeoffResponse {
  id: number;
  name: string;
  submissionDate: string;
  quotedPrice: number;
  dueDate: string;
  status: TakeoffStatus;
  blueprintFiles: BlueprintFile[];
  createdAt: string;
  updatedAt: string;
}

export interface ListTakeoffResponse {
  data: Datum[];
  meta: Meta;
}

export interface Meta {
  total: number;
  lastPage: number;
  currentPage: number;
  perPage: number;
  prev: null | number;
  next: null | number;
}

export interface Datum {
  id: number;
  userId: string;
  name: string;
  description: null | string;
  submissionDate: string;
  quotedPrice: string;
  dueDate: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: null | string;
  _count: Count;
}

export interface Count {
  blueprintFiles: number;
}
