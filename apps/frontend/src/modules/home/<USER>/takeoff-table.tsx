'use client';

import { CustomPagination } from '@/components/pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Check, X } from 'lucide-react';
import { useState } from 'react';
import { useDeleteTakeoff } from '../api/mutations';
import { Datum, ListTakeoffResponse } from '../types/takeoff';
import { DeleteTakeoffDialog } from './delete-takeoff-dialog';
import { EditTakeoff } from './edit-takeoff';
import { TakeoffTableEmpty } from './takeoff-table-empty';
import { TakeoffTableFilteredEmpty } from './takeoff-table-filtered-empty';
import { TakeoffTableLoading } from './takeoff-table-loading';
import { useRouter } from 'next/navigation';
import { useTakeoffStore } from '@/modules/takeoff/store/takeoff-store';

// Define the columns for the table
const getColumns = ({
  deleteTakeoffMutation,
  handleDeleteClick,
  handleEditClick,
  handleViewClick,
  takeoffToDelete,
}: {
  handleDeleteClick: (id: number) => void;
  handleEditClick: (id: number) => void;
  handleViewClick: (id: number) => void;
  deleteTakeoffMutation: any;
  takeoffToDelete: number | null;
}): ColumnDef<Datum>[] => [
  {
    accessorKey: 'name',
    header: () => <div className="text-left">Takeoff Name</div>,
    cell: ({ row }) => {
      const takeoff = row.original as Datum;
      return (
        <div
          onClick={() => handleViewClick(takeoff.id)}
          className="font-medium text-left hover:underline underline-offset-4 cursor-pointer"
        >
          {row.getValue('name')}
        </div>
      );
    },
    size: 200, // Set width for this column
  },
  {
    accessorKey: 'submissionDate',
    header: () => <div className="text-left">Submitted</div>,
    cell: ({ row }) => {
      const date = new Date(row.getValue('submissionDate'));
      return (
        <div className="text-left">
          {date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })}
        </div>
      );
    },
    size: 120, // Set width for this column
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-left">Status</div>,
    cell: ({ row }) => {
      const status = row.getValue('status') as string;

      // Wrapper div to ensure consistent alignment
      return (
        <div className="text-left">
          {status === 'lost' ? (
            <Badge
              variant="outline"
              className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"
            >
              <X className="h-3 w-3" />
              Lost
            </Badge>
          ) : status === 'submitted' ? (
            <Badge
              variant="outline"
              // className='bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1'
              className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1"
            >
              Submitted
            </Badge>
          ) : status === 'approved' || status === 'won' ? (
            <Badge
              variant="outline"
              className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
            >
              <Check className="h-3 w-3" />
              {status === 'won' ? 'Won' : 'Approved'}
            </Badge>
          ) : (
            <span>{status}</span>
          )}
        </div>
      );
    },
    size: 100, // Set width for this column
  },
  {
    accessorKey: 'quotedPrice',
    header: () => <div className="text-left">Price</div>,
    cell: ({ row }) => {
      const price = row.getValue('quotedPrice');
      if (!price) return <div className="text-left">-</div>;

      // Format the price as currency
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(Number(price));

      return <div className="text-left">{formatted}</div>;
    },
    size: 100, // Set width for this column
  },
  {
    accessorKey: 'dueDate',
    header: () => <div className="text-left">Due Date</div>,
    cell: ({ row }) => {
      const dueDate = row.getValue('dueDate');
      if (!dueDate) return <div className="text-left">-</div>;

      const date = new Date(dueDate as string);
      return (
        <div className="text-left">
          {date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })}
        </div>
      );
    },
    size: 120, // Set width for this column
  },
  {
    id: 'actions',
    header: () => <div className="text-center">Actions</div>,
    cell: ({ row }) => {
      // This is a type assertion to tell TypeScript that row.original has an id property
      const takeoff = row.original as Datum;
      return (
        <div className="flex justify-center gap-1">
          <Button
            variant="outline"
            size="sm"
            className="text-gray-700 border-gray-300 h-8 px-2 cursor-pointer"
            onClick={() => {
              handleViewClick(takeoff.id);
            }}
          >
            View
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="text-gray-700 border-gray-300 h-8 px-2 cursor-pointer"
            onClick={() => handleEditClick(takeoff.id)}
          >
            Edit
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="text-gray-700 border-gray-300 hover:text-red-600 hover:border-red-200 h-8 px-2 cursor-pointer"
            onClick={() => handleDeleteClick(takeoff.id)}
            disabled={
              deleteTakeoffMutation.isPending && takeoffToDelete === takeoff.id
            }
          >
            {deleteTakeoffMutation.isPending && takeoffToDelete === takeoff.id
              ? 'Deleting...'
              : 'Delete'}
          </Button>
        </div>
      );
    },
    size: 180, // Set width for this column
  },
];

interface TakeoffTableProps {
  data: ListTakeoffResponse | undefined;
  isLoading: boolean;
  currentPage: number;
  onPageChange: (page: number) => void;
  hasFilters?: boolean;
  hasSearch?: boolean;
  hasStatus?: boolean;
  hasSort?: boolean;
  onClearFilters?: () => void;
}

export function TakeoffTable({
  data,
  isLoading,
  currentPage,
  onPageChange,
  // hasFilters is not used directly, we check individual filters instead
  hasSearch = false,
  hasStatus = false,
  hasSort = false,
  onClearFilters,
}: TakeoffTableProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [takeoffToDelete, setTakeoffToDelete] = useState<number | null>(null);
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const [takeoffToEdit, setTakeoffToEdit] = useState<number | null>(null);
  const deleteTakeoffMutation = useDeleteTakeoff();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { reset } = useTakeoffStore((state) => state);

  // Find the selected takeoff data from the existing data
  const selectedTakeoffData =
    data?.data.find((item) => item.id === takeoffToEdit) || null;

  const handleDeleteClick = (id: number) => {
    setTakeoffToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const handleEditClick = (id: number) => {
    setTakeoffToEdit(id);
    setIsEditSheetOpen(true);
  };

  const handleViewClick = (id: number) => {
    reset();
    router.push(`/dashboard/takeoff/${id}`);
  };

  const handleConfirmDelete = () => {
    if (takeoffToDelete) {
      deleteTakeoffMutation.mutate(takeoffToDelete, {
        onSuccess: () => {
          toast.success('Takeoff project deleted successfully');
          queryClient.invalidateQueries({ queryKey: ['takeoff'] });
          setIsDeleteDialogOpen(false);
        },
        onError: (error) => {
          toast.error(error);
        },
      });
    }
  };

  const columns = getColumns({
    handleDeleteClick,
    handleEditClick,
    takeoffToDelete,
    handleViewClick,
    deleteTakeoffMutation,
  });

  const table = useReactTable({
    data: data?.data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return <TakeoffTableLoading />;
  }

  if (!data || data.data.length === 0) {
    // If any filters are active, show the filtered empty state
    if (hasSearch || hasStatus || hasSort) {
      return (
        <TakeoffTableFilteredEmpty
          hasSearch={hasSearch}
          hasStatus={hasStatus}
          hasSort={hasSort}
          onClearFilters={onClearFilters || (() => {})}
        />
      );
    }
    // Otherwise show the regular empty state
    return <TakeoffTableEmpty />;
  }

  return (
    <div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr
                key={headerGroup.id}
                className="border-b border-gray-200 bg-gray-50"
              >
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-4 py-3 text-sm font-medium text-gray-500"
                    style={{
                      width: header.getSize(),
                      textAlign: header.id === 'actions' ? 'center' : 'left',
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="divide-y divide-gray-200">
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id} className="bg-white hover:bg-gray-50">
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    className="px-4 py-4 text-sm"
                    style={{
                      width: cell.column.getSize(),
                      textAlign:
                        cell.column.id === 'actions' ? 'center' : 'left',
                    }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {data.meta && data.meta.lastPage > 1 && (
        <div className="mt-4 flex justify-center">
          <CustomPagination
            currentPage={currentPage}
            totalPages={data.meta.lastPage}
            hasNext={currentPage < data.meta.lastPage}
            hasPrev={currentPage > 1}
            onPageChange={onPageChange}
          />
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteTakeoffDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        isDeleting={deleteTakeoffMutation.isPending}
      />

      {/* Edit Takeoff Sheet */}
      <EditTakeoff
        isOpen={isEditSheetOpen}
        onOpenChange={setIsEditSheetOpen}
        takeoffData={selectedTakeoffData}
      />
    </div>
  );
}
