import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { CreateTakeoffResponse, ListTakeoffResponse } from '../types/takeoff';

export interface ListTakeoffParams {
  page: number;
  perPage: number;
  search?: string;
  status?: 'submitted' | 'won' | 'lost';
  orderBySubmissionDate?: 'asc' | 'desc';
}

export const useListTakeoff = ({
  page,
  perPage,
  search,
  status,
  orderBySubmissionDate,
}: ListTakeoffParams) => {
  return useQuery({
    placeholderData: keepPreviousData,
    queryKey: ['takeoff', page, perPage, search, status, orderBySubmissionDate],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListTakeoffResponse> => {
      return apiClient.get('/takeoff', {
        params: {
          page,
          perPage,
          search,
          status,
          orderBySubmissionDate,
        },
      });
    },
  });
};

export const useGetTakeoff = (id: number | null) => {
  return useQuery({
    queryKey: ['takeoff', id],
    queryFn: (): Promise<CreateTakeoffResponse> => {
      if (!id) throw new Error('Takeoff ID is required');
      return apiClient.get(`/takeoff/${id}`);
    },
    enabled: !!id, // Only run the query if id is provided
  });
};
