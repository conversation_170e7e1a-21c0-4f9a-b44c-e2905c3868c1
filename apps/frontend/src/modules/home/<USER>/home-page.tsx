'use client';

import { Card } from '@/components/ui/card';

import { CreateTakeoff } from '../components/create-takeoff';
import { useListTakeoff } from '../api/queries';
import { useState } from 'react';
import { TakeoffTable } from '../components/takeoff-table';
import { UserDropdownMenu } from '../components/user-dropdown-menu';
import { useGetMe } from '@/api/get-me';
import { TakeoffFilters } from '../components/takeoff-filters';

export function HomePage() {
  const [page, setPage] = useState(1);
  const [perPage] = useState(10);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<string | undefined>(undefined);
  const [orderBySubmissionDate, setOrderBySubmissionDate] = useState<
    'asc' | 'desc' | undefined
  >(undefined);

  // Reset to first page when filters change
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const handleStatusChange = (value: string | undefined) => {
    setStatus(value);
    setPage(1);
  };

  const handleSortChange = (value: 'asc' | 'desc' | undefined) => {
    setOrderBySubmissionDate(value);
    setPage(1);
  };

  // Function to clear all filters
  const handleClearFilters = () => {
    setSearch('');
    setStatus(undefined);
    setOrderBySubmissionDate(undefined);
    setPage(1);
  };

  const listTakeoff = useListTakeoff({
    page,
    perPage,
    search: search || undefined,
    status: status as 'submitted' | 'won' | 'lost' | undefined,
    orderBySubmissionDate,
  });

  const me = useGetMe();
  const userInitial = me.data?.fullName?.split(' ')[0][0] || 'X';

  return (
    <div className="min-h-screen bg-white w-[95%] mx-auto ">
      {/* Header */}
      <header className="border-b border-gray-200">
        <div className="px-4 py-4 flex justify-between items-center">
          <h1 className="text-xl font-semibold text-gray-900">Build Plan AI</h1>
          <UserDropdownMenu userInitial={userInitial} />
        </div>
      </header>

      {/* Main Content */}
      <main className="px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-semibold text-gray-900">Takeoffs</h2>
          <CreateTakeoff />
        </div>

        <Card className="overflow-hidden py-4 px-4 rounded-sm">
          <TakeoffFilters
            search={search}
            status={status}
            sort={orderBySubmissionDate}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
            onSortChange={handleSortChange}
            isLoading={listTakeoff.isFetching && !listTakeoff.isLoading}
          />

          <TakeoffTable
            data={listTakeoff.data}
            isLoading={listTakeoff.isLoading}
            currentPage={page}
            onPageChange={(newPage) => setPage(newPage)}
            hasFilters={!!search || !!status || !!orderBySubmissionDate}
            hasSearch={!!search}
            hasStatus={!!status}
            hasSort={!!orderBySubmissionDate}
            onClearFilters={handleClearFilters}
          />
        </Card>
      </main>
    </div>
  );
}
