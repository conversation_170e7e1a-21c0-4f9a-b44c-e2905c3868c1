import { useMutation } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import type {
  ForgotPasswordPayload,
  ForgotPasswordResponse,
} from '../types/auth';
import type { LoginPayload, LoginResponse } from '../types/login';
import type { ResetPassword } from '../types/reset-password';
import type { SignupPayload, SignupResponse } from '../types/signup';
import type { VerifyOtp } from '../types/verify-otp';

export const useForgotPassword = () => {
  return useMutation({
    mutationFn: (
      data: ForgotPasswordPayload,
    ): Promise<ForgotPasswordResponse> =>
      apiClient.post('/auth/forgot-password', data),
  });
};

export const useLogin = () => {
  return useMutation({
    mutationFn: (data: LoginPayload): Promise<LoginResponse> =>
      apiClient.post('/auth/login', data),
  });
};

export const useResetPassword = () => {
  return useMutation({
    mutationFn: async (values: ResetPassword) => {
      return await apiClient.post('auth/reset-password', values);
    },
  });
};

export const useSignup = () => {
  return useMutation({
    mutationFn: (data: SignupPayload): Promise<SignupResponse> =>
      apiClient.post('/auth/sign-up', data),
  });
};

export const useResendOtp = () => {
  return useMutation({
    mutationFn: (data: { userId: string }) =>
      apiClient.post(`/auth/email-verification/send-otp`, data),
  });
};

export const useVerifyOtp = () => {
  return useMutation({
    mutationFn: (data: { otp: string; userId: string }): Promise<VerifyOtp> =>
      apiClient.post(`/auth/email-verification/verify-otp`, data),
  });
};
