export interface User {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  phoneNumberVerifiedAt: any;
  emailVerifiedAt: string;
  resetToken: any;
  resetTokenExpiresAt: any;
  createdAt: string;
  updatedAt: string;
  deletedAt: any;
  status: string;
  roles: Role[];
  organization: Organization;
}

export interface Role {
  id: number;
  name: string;
  description: any;
  createdAt: string;
  updatedAt: string;
  deletedAt: any;
}

export interface Organization {
  id: number;
  userId: string;
  name: string;
  description: any;
  createdAt: string;
  updatedAt: string;
}
