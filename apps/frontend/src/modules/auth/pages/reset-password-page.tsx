'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, CheckCircle, EyeIcon, EyeOffIcon } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from '@/lib/toast';
import * as z from 'zod';
import { useResetPassword } from '../api/mutations';
import { ErrorMessage } from '@/components/error-message';
import { AuthHeader } from '../components/header';
import { passwordSchema } from '@/schema/password-schema';

const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

export function ResetPasswordPage() {
  const [isSuccess, setIsSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const resetPasswordMutation = useResetPassword();

  const onSubmit = (data: ResetPasswordFormData) => {
    const resetPasswordData = {
      password: data.password,
      token: token,
    };

    resetPasswordMutation.mutate(resetPasswordData, {
      onSuccess: () => {
        setIsSuccess(true);
        toast.success('Password reset successfully');
      },
      onError: (error) => {
        toast.error(error);
      },
    });
  };

  const handleBackToLogin = () => {
    router.push('/');
  };

  return (
    <div className="grid min-h-screen w-full">
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full max-w-md space-y-6 p-2 sm:p-6">
          <AuthHeader />

          <Card className="w-full">
            {isSuccess ? (
              <CardContent className="flex flex-col items-center justify-center space-y-4 pt-6">
                <CheckCircle className="h-16 w-16 text-green-500" />
                <CardTitle className="text-2xl font-semibold text-center">
                  Password Reset Successful
                </CardTitle>
                <p className="text-center text-muted-foreground">
                  Your password has been successfully reset. You can now log in
                  with your new password.
                </p>
                <Button
                  className="w-full text-base"
                  size="lg"
                  variant={'link'}
                  onClick={handleBackToLogin}
                >
                  <ArrowLeft />
                  Back to Login
                </Button>
              </CardContent>
            ) : (
              <>
                <CardHeader className="space-y-1 text-center">
                  <CardTitle className="text-2xl font-semibold mb-2">
                    Reset password
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Enter your new password below to reset your account.
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    <div className="space-y-1.5">
                      <Label htmlFor="password">New password</Label>
                      <div className="relative">
                        <Input
                          id="password"
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter new password"
                          {...register('password')}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOffIcon className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <EyeIcon className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                      </div>
                      <ErrorMessage error={errors.password} />
                    </div>

                    <div className="space-y-1.5">
                      <Label htmlFor="confirmPassword">
                        Confirm new password
                      </Label>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder="Confirm new password"
                          {...register('confirmPassword')}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                        >
                          {showConfirmPassword ? (
                            <EyeOffIcon className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <EyeIcon className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                      </div>
                      <ErrorMessage error={errors.confirmPassword} />
                    </div>

                    <Button
                      className="w-full text-base"
                      size="lg"
                      type="submit"
                      disabled={resetPasswordMutation.isPending}
                    >
                      {resetPasswordMutation.isPending ? (
                        <>
                          Resetting...
                          <Spinner className="ml-2" />
                        </>
                      ) : (
                        'Reset password'
                      )}
                    </Button>
                  </form>

                  <Link
                    href="/"
                    className="flex items-center justify-center gap-2 text-sm text-primary hover:underline"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Login
                  </Link>
                </CardContent>
              </>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
