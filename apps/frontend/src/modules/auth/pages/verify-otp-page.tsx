'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { Spinner } from '@/components/ui/spinner';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from '@/lib/toast';
import * as z from 'zod';
import { useResendOtp, useVerifyOtp } from '../api/mutations';
import { errorParser } from '@/lib/error-parser';
import { useRouter } from 'next/navigation';
import { ErrorMessage } from '@/components/error-message';
import { useAuthStore } from '@/store/auth-store';
import { AuthHeader } from '../components/header';

const verifyOtpSchema = z.object({
  otp: z.string().length(6, 'Please enter a valid OTP'),
});

type VerifyOtpFormData = z.infer<typeof verifyOtpSchema>;

const formatPhoneNumber = (phoneNumber: string) => {
  const countryCode = phoneNumber.startsWith('+')
    ? phoneNumber.slice(0, 3)
    : phoneNumber.slice(0, 2);
  const number = phoneNumber.slice(countryCode.length);
  const lastThreeDigits = number.slice(-3);
  const maskedPart = '*'.repeat(Math.max(0, number.length - 3));
  return `${countryCode}${maskedPart}${lastThreeDigits}`;
};

export function VerifyOtpPage() {
  const [timeLeft, setTimeLeft] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const authStore = useAuthStore((state) => state);

  const router = useRouter();
  const form = useForm<VerifyOtpFormData>({
    resolver: zodResolver(verifyOtpSchema),
    defaultValues: {
      otp: '',
    },
  });
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    setFocus,
  } = form;

  const verifyOtpMutation = useVerifyOtp();
  const resendOtpMutation = useResendOtp();

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  const onSubmit = (data: VerifyOtpFormData) => {
    if (!authStore.userId) {
      toast.error('User not found');
      return;
    }

    const userId = String(authStore.userId);

    const payload = {
      otp: data.otp,
      userId,
    };

    verifyOtpMutation.mutate(payload, {
      onSuccess: (response) => {
        const { accessToken, refreshToken } = response;

        toast.success('OTP verified successfully');
        useAuthStore.getState().setToken(accessToken, refreshToken);
        router.push('/dashboard/home');
      },
      onError: (error) => {
        toast.error(error);
      },
    });
  };

  const handleResendOtp = () => {
    if (!canResend) return;
    if (!authStore.userId) {
      toast.error('User not found');
      return;
    }

    const userId = authStore.userId;

    resendOtpMutation.mutate(
      { userId },
      {
        onSuccess: () => {
          setValue('otp', '');
          setFocus('otp');
          toast.success('OTP resend successfully');
          setTimeLeft(30);
          setCanResend(false);
        },
        onError: (error) => {
          toast.error(errorParser(error.message));
        },
      },
    );
  };

  return (
    <div className="grid min-h-screen w-full">
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full max-w-md space-y-8 p-6">
          <AuthHeader />

          <Card className="w-full">
            <CardHeader className="space-y-3">
              <CardTitle className="text-2xl font-semibold text-center">
                Verify OTP
              </CardTitle>
              <p className="text-center text-muted-foreground">
                Enter the OTP sent to{' '}
                <span className="font-medium text-muted-foreground">
                  {formatPhoneNumber(authStore.phoneNumber ?? '')}
                </span>
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <Form {...form}>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <FormField
                      control={control}
                      name="otp"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <InputOTP maxLength={6} {...field}>
                              <InputOTPGroup className="flex justify-center gap-4 w-full">
                                {[1, 2, 3, 4, 5, 6].map((item, index) => (
                                  <InputOTPSlot
                                    key={index}
                                    index={index}
                                    className="rounded-md border border-input"
                                  />
                                ))}
                              </InputOTPGroup>
                            </InputOTP>
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <ErrorMessage error={errors.otp} />
                  </div>

                  <div className="space-y-4">
                    <Button
                      className="w-full text-base"
                      size="lg"
                      type="submit"
                      disabled={verifyOtpMutation.isPending}
                    >
                      {verifyOtpMutation.isPending ? (
                        <>
                          Verifying...
                          <Spinner className="ml-2" />
                        </>
                      ) : (
                        'Verify OTP'
                      )}
                    </Button>

                    <div className="text-center space-y-3">
                      <p className="text-sm text-muted-foreground">
                        Didn&apos;t receive the OTP?{' '}
                        <Button
                          type="button"
                          variant="link"
                          className="p-0 h-auto font-semibold"
                          disabled={!canResend || resendOtpMutation.isPending}
                          onClick={handleResendOtp}
                        >
                          {resendOtpMutation.isPending ? (
                            <>
                              Resending...
                              <Spinner className="ml-2 h-3 w-3" />
                            </>
                          ) : canResend ? (
                            'Resend OTP'
                          ) : (
                            `Resend in ${timeLeft}s`
                          )}
                        </Button>
                      </p>

                      <Link
                        href="/"
                        className="flex items-center justify-center gap-2 text-sm text-primary hover:underline"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        Back to Login
                      </Link>
                    </div>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
