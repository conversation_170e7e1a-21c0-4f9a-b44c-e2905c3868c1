'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useAuthStore } from '@/store/auth-store';
import { zodResolver } from '@hookform/resolvers/zod';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useLogin, useResendOtp } from '../api/mutations';
import { AuthHeader } from '../components/header';
import { TermsAndPrivacy } from '../components/terms-and-privacy';
import { AxiosError } from 'axios';

type LoginError = {
  code: 'EMAIL_NOT_VERIFIED';
  email: string;
  message: string;
};

export function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const authStore = useAuthStore((state) => state);
  const resendOtpMutation = useResendOtp();

  const loginSchema = z.object({
    email: z.string().email('Invalid email address'),
    password: z.string().min(1, 'Please enter your password'),
  });

  type LoginFormData = z.infer<typeof loginSchema>;

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const loginMutation = useLogin();

  const onSubmit = async (data: LoginFormData) => {
    const payload = {
      email: String(data.email),
      password: data.password,
    };

    loginMutation.mutate(payload, {
      onSuccess: (response) => {
        toast.success('Login successful');
        const userId = response?.user?.id;
        authStore.setUserId(String(userId));

        const token = response.accessToken;
        const refreshToken = response.refreshToken;
        authStore.setToken(token, refreshToken);
        router.push('/dashboard/home');
      },
      onError: (err) => {
        const error = err as AxiosError;
        const errorData = error.response?.data as LoginError;

        if (errorData.code === 'EMAIL_NOT_VERIFIED') {
          const userId = errorData.email;
          toast.error(errorData);
          authStore.setUserId(String(userId));
          resendOtpMutation.mutate({ userId });
          router.push('/auth/verify-otp');
          return;
        }

        toast.error(errorData);
      },
    });
  };

  return (
    <div className="grid min-h-screen w-full">
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full max-w-md space-y-6 p-2 sm:p-6">
          <AuthHeader />

          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-2xl font-semibold text-center">
                Login
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    {...register('email')}
                    placeholder="Enter your email"
                  />

                  <ErrorMessage error={errors.email} />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">Password</Label>
                    <Link
                      href="/auth/forgot-password"
                      className="text-sm text-primary hover:underline"
                    >
                      Forgot password?
                    </Link>
                  </div>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      {...register('password')}
                      placeholder="Enter your password"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <EyeIcon className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  <ErrorMessage error={errors.password} />
                </div>

                <Button
                  className="w-full text-base"
                  size="lg"
                  type="submit"
                  disabled={loginMutation.isPending}
                >
                  {loginMutation.isPending ? (
                    <>
                      Logging in...
                      <Spinner className="ml-2" />
                    </>
                  ) : (
                    'Login'
                  )}
                </Button>
              </form>

              <TermsAndPrivacy />
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <div className="text-sm text-center text-muted-foreground">
                Are you a new user?{' '}
                <Link
                  href="/auth/signup"
                  className="text-primary hover:underline font-medium"
                >
                  Create account
                </Link>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
