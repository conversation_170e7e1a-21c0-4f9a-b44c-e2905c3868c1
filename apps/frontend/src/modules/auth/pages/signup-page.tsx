'use client';

import { ErrorMessage } from '@/components/error-message';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useAuthStore } from '@/store/auth-store';
import { zodResolver } from '@hookform/resolvers/zod';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import PhoneInput, { isValidPhoneNumber } from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import * as z from 'zod';
import { useSignup } from '../api/mutations';
import { AuthHeader } from '../components/header';
import { TermsAndPrivacy } from '../components/terms-and-privacy';
import { passwordSchema } from '@/schema/password-schema';

export function SignupPage() {
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const authStore = useAuthStore((state) => state);

  const signupSchema = z.object({
    fullName: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.string().email('Invalid email address'),
    phoneNumber: z.string().refine((value) => isValidPhoneNumber(value), {
      message: 'Invalid phone number',
    }),
    password: passwordSchema,
    organizationName: z.string(),
  });

  type SignupFormData = z.infer<typeof signupSchema>;

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phoneNumber: '',
      password: '',
      organizationName: '',
    },
  });

  const signupMutation = useSignup();

  const onSubmit = async (data: SignupFormData) => {
    signupMutation.mutate(data, {
      onSuccess: (response) => {
        toast.success('Signup successful');
        const userId = response?.userId;
        console.log({ userId });
        authStore.setUserId(String(userId));
        router.push('/auth/verify-otp');
      },
      onError: (error) => {
        toast.error(error);
      },
    });
  };

  return (
    <div className="grid min-h-screen w-full">
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full max-w-md space-y-6 p-2 sm:p-6">
          <AuthHeader />

          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-2xl font-semibold text-center">
                Create account
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="space-y-1.5">
                  <Label htmlFor="fullName">Full name</Label>
                  <Input
                    id="fullName"
                    {...register('fullName')}
                    placeholder="Enter your full name"
                  />
                  <ErrorMessage error={errors.fullName} />
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="Enter your email"
                  />
                  <ErrorMessage error={errors.email} />
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="phoneNumber">Phone number</Label>
                  <Controller
                    name="phoneNumber"
                    control={control}
                    render={({ field }) => (
                      <PhoneInput
                        {...field}
                        international
                        countryCallingCodeEditable={false}
                        defaultCountry="IN"
                        style={{ outline: 'none' }}
                        className="flex no-border-inputs focus-visible:ring-[0px] h-10 w-full rounded-md border border-input bg-background px-3 py-0 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground  disabled:cursor-not-allowed disabled:opacity-50"
                      />
                    )}
                  />
                  <ErrorMessage error={errors.phoneNumber} />
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="organizationName">Organisation name</Label>
                  <Input
                    id="organizationName"
                    {...register('organizationName')}
                    placeholder="Enter your organisation name"
                  />
                  <ErrorMessage error={errors.organizationName} />
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      {...register('password')}
                      placeholder="Enter password"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <EyeIcon className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  <ErrorMessage error={errors.password} />
                </div>

                <Button
                  className="w-full text-base"
                  size="lg"
                  type="submit"
                  disabled={signupMutation.isPending}
                >
                  {signupMutation.isPending ? (
                    <>
                      Signing up...
                      <Spinner className="ml-2" />
                    </>
                  ) : (
                    'Sign up'
                  )}
                </Button>
              </form>

              <TermsAndPrivacy />
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <div className="text-sm text-center text-muted-foreground">
                Already have an account?{' '}
                <Link
                  href="/"
                  className="text-primary hover:underline font-medium"
                >
                  Login
                </Link>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
