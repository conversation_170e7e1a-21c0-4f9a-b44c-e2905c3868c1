'use client';

import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import 'react-phone-number-input/style.css';
import { toast } from '@/lib/toast';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { ArrowLeft } from 'lucide-react';
import { Spinner } from '@/components/ui/spinner';
import { useForgotPassword } from '../api/mutations';
import { ErrorMessage } from '@/components/error-message';
import React from 'react';
import { AuthHeader } from '../components/header';
import { Input } from '@/components/ui/input';

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export function ForgotPasswordPage() {
  const {
    handleSubmit,
    register,
    formState: { errors },
    reset,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const forgotPasswordMutation = useForgotPassword();

  const onSubmit = (data: ForgotPasswordFormData) => {
    const payload = {
      ...data,
    };

    forgotPasswordMutation.mutate(payload, {
      onSuccess: () => {
        reset({});
        toast.success('Password reset link send successfully');
      },
      onError: (error) => {
        toast.error(error);
      },
    });
  };

  return (
    <div className="grid min-h-screen w-full">
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full max-w-md space-y-6 p-2 sm:p-6">
          <AuthHeader />

          <Card className="w-full">
            <CardHeader className="space-y-1 text-center">
              <CardTitle className="text-2xl font-semibold mb-2">
                Forgot password?
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Enter email address to receive a password reset link
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="space-y-1.5">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="Enter your email"
                  />
                  <ErrorMessage error={errors.email} />
                </div>

                <Button
                  className="w-full text-base"
                  size="lg"
                  type="submit"
                  disabled={forgotPasswordMutation.isPending}
                >
                  {forgotPasswordMutation.isPending ? (
                    <>
                      Sending...
                      <Spinner className="ml-2" />
                    </>
                  ) : (
                    'Send reset link'
                  )}
                </Button>
              </form>

              <Link
                href="/"
                className="flex items-center justify-center gap-2 text-sm text-primary hover:underline"
              >
                <ArrowLeft className="h-4 w-4" />
                Go back to Login
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
