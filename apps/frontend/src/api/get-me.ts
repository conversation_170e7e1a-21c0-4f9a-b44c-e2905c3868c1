import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { secondsToMilliseconds } from 'date-fns';

const getMe = (): Promise<IGetMe> => {
  return apiClient.get('/users/me');
};

export const useGetMe = () => {
  return useQuery({
    queryKey: ['me'],
    queryFn: getMe,
    staleTime: secondsToMilliseconds(5),
  });
};

export interface IGetMe {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  phoneNumberVerifiedAt: null;
  emailVerifiedAt: string;
  profilePic: null | {
    id: number;
    file_url: string;
  };
  resetToken: null;
  resetTokenExpiresAt: null;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
  status: string;
  roles: Role[];
  organization: Organization;
}

interface Organization {
  id: number;
  userId: string;
  name: string;
  description: null;
  createdAt: string;
  updatedAt: string;
}

interface Role {
  id: number;
  name: string;
  description: null;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
}
