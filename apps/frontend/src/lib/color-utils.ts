/**
 * Generates a random hexadecimal color code.
 * @returns A string representing a random hex color (e.g., "#RRGGBB").
 */
export function generateRandomHexColor(): string {
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += Math.floor(Math.random() * 16).toString(16);
  }
  return color;
}

/**
 * Generates a random shade based on a given base color.
 * For simplicity, this example generates another random color.
 * A more sophisticated version might lighten or darken the base color.
 * @param _baseColor - The base color (currently unused in this simple version).
 * @returns A string representing a random hex color for the shade.
 */
export function generateRandomShade(_baseColor?: string): string {
  // For now, let's just generate another fully random color for the shade.
  // In a real scenario, you might want to derive this from the baseColor
  // (e.g., make it a lighter or darker version).
  return generateRandomHexColor();
}

/**
 * Example of a more sophisticated shade generator (darkens a color).
 * This is not used by default but shows a potential improvement.
 * @param hexColor The base hex color string (e.g., "#RRGGBB").
 * @param percent The percentage to darken the color by (0-100).
 * @returns A new hex color string that is darker.
 */
export function darkenColor(hexColor: string, percent: number): string {
  let r = parseInt(hexColor.substring(1, 3), 16);
  let g = parseInt(hexColor.substring(3, 5), 16);
  let b = parseInt(hexColor.substring(5, 7), 16);

  const factor = 1 - percent / 100;

  r = Math.max(0, Math.floor(r * factor));
  g = Math.max(0, Math.floor(g * factor));
  b = Math.max(0, Math.floor(b * factor));

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

/**
 * Example of a more sophisticated shade generator (lightens a color).
 * This is not used by default but shows a potential improvement.
 * @param hexColor The base hex color string (e.g., "#RRGGBB").
 * @param percent The percentage to lighten the color by (0-100).
 * @returns A new hex color string that is lighter.
 */
export function lightenColor(hexColor: string, percent: number): string {
  let r = parseInt(hexColor.substring(1, 3), 16);
  let g = parseInt(hexColor.substring(3, 5), 16);
  let b = parseInt(hexColor.substring(5, 7), 16);

  const factor = percent / 100;

  r = Math.min(255, Math.floor(r + (255 - r) * factor));
  g = Math.min(255, Math.floor(g + (255 - g) * factor));
  b = Math.min(255, Math.floor(b + (255 - b) * factor));

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}
