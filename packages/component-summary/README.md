# @repo/component-summary

A shared package for calculating component measurements and summaries from blueprint drawings. This package provides pure calculation functions that can be used by both frontend (React) and backend (NestJS) applications.

## Features

- **Pure Functions**: No React dependencies, works in any JavaScript/TypeScript environment
- **Comprehensive Calculations**: Supports area, length, perimeter, and point counting
- **Multiple Geometry Types**: Rectangle, circle, ellipse, freehand, point-to-point, and curve shapes
- **Dynamic Measurements**: Real-time calculations based on scale and paper size
- **Component Aggregation**: Summary totals by component type
- **Export Support**: Filter data for aggregate or page-specific exports

## Installation

This package is part of the monorepo workspace and can be imported using:

```typescript
import { generateComponentSummary } from '@repo/component-summary';
```

## Core API

### Main Functions

```typescript
// Generate complete component summary
const summaryData = generateComponentSummary(
  components,
  drawings,
  scale,
  paperSize,
);

// Calculate project totals
const totals = calculateProjectTotals(summaryData);

// Generate filtered summary (aggregate or specific page)
const filteredSummary = generateFilteredComponentSummary(
  components,
  drawings,
  scale,
  paperSize,
  'aggregate', // or 'specific'
  blueprintImageId, // required for 'specific'
);
```

### Individual Calculations

```typescript
// Calculate measurements for a single drawing
const measurements = calculateDrawingMeasurements(
  drawing.config,
  scale,
  paperSize,
);

// Get component totals
const componentTotals = calculateComponentTotalMeasurements(
  drawings,
  geometryType,
  scale,
  paperSize,
);
```

## Types

### Core Types

- `Component`: Component definition with geometry type and metadata
- `Drawing`: Drawing data with configuration and measurements
- `ScaleInfo`: Blueprint scale information (1:8, etc.)
- `PaperSize`: Paper dimensions and units
- `ComponentSummaryData`: Complete component summary with totals and items
- `ProjectTotals`: Aggregated project measurements

### Geometry Types

- `surface`: Area-based components (walls, floors, etc.)
- `edge`: Length-based components (doors, windows, etc.)
- `point`: Count-based components (fixtures, outlets, etc.)

## Usage Examples

### Frontend (React Hook)

```typescript
import { useMemo } from 'react';
import { generateComponentSummary } from '@repo/component-summary';

export function useComponentSummary(components, drawings, scale, paperSize) {
  const summaryData = useMemo(() => {
    if (!scale) return new Map();

    return generateComponentSummary(components, drawings, scale, paperSize);
  }, [components, drawings, scale, paperSize]);

  return { summaryData };
}
```

### Backend (NestJS Service)

```typescript
import { Injectable } from '@nestjs/common';
import {
  generateFilteredComponentSummary,
  calculateProjectTotals,
} from '@repo/component-summary';

@Injectable()
export class ComponentSummaryService {
  generateExport(
    components: Component[],
    drawings: Drawing[],
    scale: ScaleInfo,
    paperSize: PaperSize,
    exportType: 'aggregate' | 'specific',
    blueprintImageId?: string,
  ) {
    const summaryData = generateFilteredComponentSummary(
      components,
      drawings,
      scale,
      paperSize,
      exportType,
      blueprintImageId,
    );

    const totals = calculateProjectTotals(summaryData);

    return {
      components: Array.from(summaryData.values()),
      totals,
    };
  }
}
```

## Supported Shape Types

### Rectangle

- Configuration: `{ type: 'rectangle', width: '100', height: '50' }`
- Calculations: Area, perimeter

### Circle

- Configuration: `{ type: 'circle', radius: '25' }`
- Calculations: Area, circumference

### Ellipse

- Configuration: `{ type: 'ellipse', radiusX: '30', radiusY: '20' }`
- Calculations: Area, perimeter (Ramanujan's approximation)

### Freehand

- Configuration: `{ type: 'freehand', points: '[0,0,100,0,100,100]', closed: 'true' }`
- Calculations: Area + perimeter (closed) or length (open)

### Point-to-Point

- Configuration: `{ type: 'point-to-point', points: '[0,0,100,0,100,100]', closed: 'false' }`
- Calculations: Length (open) or area + perimeter (closed)

### Curve

- Configuration: `{ type: 'curve', points: '[0,0,50,25,100,0]' }`
- Calculations: Length

## Constants

```typescript
import { ARCH_PAPER_SIZES, DEFAULT_PAPER_SIZE } from '@repo/component-summary';

// Available paper sizes: ARCH_A, ARCH_B, ARCH_C, ARCH_D, ARCH_E1, ARCH_E
const paperSize = ARCH_PAPER_SIZES.ARCH_D; // 36" x 24"
```

## Build

```bash
# Build the package
pnpm build

# Development mode (watch)
pnpm dev
```

## Migration Notes

This package was created by migrating calculation functions from the frontend takeoff module. The functions maintain identical calculation logic to ensure consistency between frontend display and backend exports.

### Migrated From:

- `apps/frontend/src/modules/takeoff/utils/dynamic-measurements.ts`
- `apps/frontend/src/modules/takeoff/utils/distance-utils.ts`
- `apps/frontend/src/modules/takeoff/types/`
- `apps/frontend/src/modules/takeoff/constants/`

### Key Differences:

- Removed React dependencies (hooks, state management)
- Added export filtering capabilities
- Improved TypeScript typing
- Added comprehensive API for backend usage
