/**
 * Main API functions for generating component summaries
 */

import {
  Component,
  Drawing,
  ScaleInfo,
  PaperSize,
  ComponentSummaryData,
  DrawingSummaryItem,
  ProjectTotals,
  ComponentTotalMetrics,
  GeometryType,
} from '../types';
import { calculateComponentTotalMeasurements } from './component-aggregator';
import { extractDrawingMeasurementDynamic } from './drawing-calculator';

/**
 * Generate complete component summary for all provided components and drawings
 */
export function generateComponentSummary(
  components: Component[],
  drawings: Drawing[],
  scale: ScaleInfo,
  paperSize: PaperSize,
): Map<number, ComponentSummaryData> {
  const summaryMap = new Map<number, ComponentSummaryData>();

  components.forEach((component) => {
    const componentDrawings = drawings.filter(
      (d) => d.componentId === component.id,
    );

    const drawingSummaryItems = componentDrawings.map((drawing) =>
      createDrawingSummaryItem(
        drawing,
        component.geometryType,
        scale,
        paperSize,
      ),
    );

    const totalMetrics = calculateComponentTotalMeasurements(
      componentDrawings,
      component.geometryType,
      scale,
      paperSize,
    );

    summaryMap.set(component.id, {
      componentId: component.id,
      componentName: component.name,
      componentType: component.geometryType,
      totalMetrics,
      drawings: componentDrawings, // Full Drawing objects for tooltip
      drawingSummaryItems, // Summary items for display
      isExpanded: false, // Default to collapsed, will be managed by UI
    });
  });

  return summaryMap;
}

/**
 * Create a drawing summary item for display
 */
export function createDrawingSummaryItem(
  drawing: Drawing,
  componentType: GeometryType,
  scale: ScaleInfo,
  paperSize: PaperSize,
): DrawingSummaryItem {
  const displayId = generateDrawingDisplayId(drawing, componentType);
  const measurement = extractDrawingMeasurementDynamic(
    drawing,
    componentType,
    scale,
    paperSize,
  );

  return {
    id: drawing.id,
    displayId,
    measurement,
  };
}

/**
 * Generate display ID for a drawing
 */
export function generateDrawingDisplayId(
  drawing: Drawing,
  _componentType: GeometryType,
): string {
  return `ID ${drawing.id}`;
}

/**
 * Calculate project totals from component summary data
 */
export function calculateProjectTotals(
  summaryData: Map<number, ComponentSummaryData>,
): ProjectTotals {
  let totalSurfaceArea = 0;
  let totalEdgeLength = 0;
  let totalPointCount = 0;

  let areaUnit = 'sq ft';
  let lengthUnit = 'ft';

  for (const componentData of summaryData.values()) {
    const { totalMetrics, componentType } = componentData;

    switch (componentType) {
      case 'surface':
        if (totalMetrics.type === 'area') {
          totalSurfaceArea += totalMetrics.value;
          areaUnit = totalMetrics.unit;
        }
        break;
      case 'edge':
        if (totalMetrics.type === 'length') {
          totalEdgeLength += totalMetrics.value;
          lengthUnit = totalMetrics.unit;
        }
        break;
      case 'point':
        if (totalMetrics.type === 'count') {
          totalPointCount += totalMetrics.value;
        }
        break;
    }
  }

  return {
    totalSurfaceArea: {
      type: 'area',
      value: totalSurfaceArea,
      formattedValue: totalSurfaceArea.toFixed(2),
      unit: areaUnit,
    },
    totalEdgeLength: {
      type: 'length',
      value: totalEdgeLength,
      formattedValue: totalEdgeLength.toFixed(2),
      unit: lengthUnit,
    },
    totalPointCount: {
      type: 'count',
      value: totalPointCount,
      formattedValue: totalPointCount.toString(),
      unit: 'points',
    },
  };
}

/**
 * Format measurement value for display
 */
export function formatMeasurement(value: number, unit: string): string {
  const rounded = Math.round(value * 100) / 100;
  return `${rounded} ${unit}`;
}

/**
 * Filter components and drawings based on export type
 */
export function filterDataForExport(
  components: Component[],
  drawings: Drawing[],
  filterType: 'aggregate' | 'specific',
  blueprintImageId?: string,
): {
  filteredComponents: Component[];
  filteredDrawings: Drawing[];
} {
  if (filterType === 'aggregate') {
    // For aggregate, include all components and drawings
    return {
      filteredComponents: components,
      filteredDrawings: drawings,
    };
  } else {
    // For specific, filter by blueprintImageId
    if (!blueprintImageId) {
      throw new Error('blueprintImageId is required for specific export');
    }

    const filteredComponents = components.filter(
      (component) =>
        component.selectionType === 'currentPage' &&
        component.blueprintImageId === blueprintImageId,
    );

    const filteredDrawings = drawings.filter(
      (drawing) => drawing.blueprintImageId === blueprintImageId,
    );

    return {
      filteredComponents,
      filteredDrawings,
    };
  }
}

/**
 * Generate component summary with filtering support
 */
export function generateFilteredComponentSummary(
  components: Component[],
  drawings: Drawing[],
  scale: ScaleInfo,
  paperSize: PaperSize,
  filterType: 'aggregate' | 'specific',
  blueprintImageId?: string,
): Map<number, ComponentSummaryData> {
  const { filteredComponents, filteredDrawings } = filterDataForExport(
    components,
    drawings,
    filterType,
    blueprintImageId,
  );

  return generateComponentSummary(
    filteredComponents,
    filteredDrawings,
    scale,
    paperSize,
  );
}
