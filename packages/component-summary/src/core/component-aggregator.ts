/**
 * Component aggregation utilities for calculating total measurements
 */

import {
  Drawing,
  GeometryType,
  ScaleInfo,
  PaperSize,
  ComponentTotalMetrics,
} from '../types';
import { calculateDrawingMeasurements } from './drawing-calculator';

/**
 * Calculate total measurements for a component from all its drawings
 */
export function calculateComponentTotalMeasurements(
  drawings: Drawing[],
  geometryType: GeometryType,
  scale: ScaleInfo,
  paperSize: PaperSize,
): ComponentTotalMetrics {
  switch (geometryType) {
    case 'surface':
      return calculateTotalAreaDynamic(drawings, scale, paperSize);
    case 'edge':
      return calculateTotalLengthDynamic(drawings, scale, paperSize);
    case 'point':
      return calculatePointCountDynamic(drawings);
    default:
      return {
        type: 'count',
        value: 0,
        formattedValue: '0',
        unit: '',
      };
  }
}

/**
 * Calculate total area for surface components dynamically
 */
function calculateTotalAreaDynamic(
  drawings: Drawing[],
  scale: ScaleInfo,
  paperSize: PaperSize,
): ComponentTotalMetrics {
  let totalArea = 0;
  let unit = 'sq ft'; // Default unit

  drawings.forEach((drawing) => {
    const measurements = calculateDrawingMeasurements(
      drawing.config,
      scale,
      paperSize,
    );

    if (measurements?.area) {
      totalArea += measurements.area.value;
      unit = measurements.area.unit; // Use the unit from measurements
    }
  });

  return {
    type: 'area',
    value: totalArea,
    formattedValue: totalArea.toFixed(2),
    unit,
  };
}

/**
 * Calculate total length for edge components dynamically
 */
function calculateTotalLengthDynamic(
  drawings: Drawing[],
  scale: ScaleInfo,
  paperSize: PaperSize,
): ComponentTotalMetrics {
  let totalLength = 0;
  let unit = 'ft'; // Default unit

  drawings.forEach((drawing) => {
    const measurements = calculateDrawingMeasurements(
      drawing.config,
      scale,
      paperSize,
    );

    if (measurements?.length) {
      totalLength += measurements.length.value;
      unit = measurements.length.unit; // Use the unit from measurements
    }
  });

  return {
    type: 'length',
    value: totalLength,
    formattedValue: totalLength.toFixed(2),
    unit,
  };
}

/**
 * Calculate count for point components (unchanged)
 */
function calculatePointCountDynamic(
  drawings: Drawing[],
): ComponentTotalMetrics {
  return {
    type: 'count',
    value: drawings.length,
    formattedValue: drawings.length.toString(),
    unit: 'points',
  };
}
