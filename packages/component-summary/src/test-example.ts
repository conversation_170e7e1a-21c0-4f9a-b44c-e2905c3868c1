/**
 * Example usage of the component summary package
 * This file demonstrates how to use the package functions
 */

import {
  generateComponentSummary,
  calculateProjectTotals,
  Component,
  Drawing,
  ScaleInfo,
  PaperSize,
  DEFAULT_PAPER_SIZE,
} from './index';

// Example data
const mockScale: ScaleInfo = {
  num_metric: 1,
  num_unit: 'inch',
  den_metric: 8,
  den_unit: 'ft',
};

const mockPaperSize: PaperSize = DEFAULT_PAPER_SIZE;

const mockComponents: Component[] = [
  {
    id: 1,
    name: 'Wall Component',
    selectionType: 'allPages',
    blueprintFileId: 'file-1',
    geometryType: 'surface',
    color: '#ff0000',
    shade: 'solid',
  },
  {
    id: 2,
    name: 'Door Component',
    selectionType: 'allPages',
    blueprintFileId: 'file-1',
    geometryType: 'edge',
    color: '#00ff00',
    shade: 'solid',
  },
];

const mockDrawings: Drawing[] = [
  {
    id: 1,
    blueprintImageId: 'image-1',
    componentId: 1,
    config: {
      type: 'rectangle',
      width: '100',
      height: '50',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    deletedAt: null,
  },
  {
    id: 2,
    blueprintImageId: 'image-1',
    componentId: 2,
    config: {
      type: 'freehand',
      points: JSON.stringify([0, 0, 100, 0, 100, 100, 0, 100]),
      closed: 'false',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    deletedAt: null,
  },
];

// Test the package functions
export function testPackage() {
  console.log('Testing @repo/component-summary package...');

  // Generate component summary
  const summaryData = generateComponentSummary(
    mockComponents,
    mockDrawings,
    mockScale,
    mockPaperSize,
  );

  console.log('Generated summary data:');
  for (const [componentId, data] of summaryData.entries()) {
    console.log(`Component ${componentId}:`, {
      name: data.componentName,
      type: data.componentType,
      totalMetrics: data.totalMetrics,
      drawingCount: data.drawings.length,
    });
  }

  // Calculate project totals
  const totals = calculateProjectTotals(summaryData);
  console.log('Project totals:', totals);

  return {
    summaryData,
    totals,
  };
}
