/**
 * Standard paper size definitions with their dimensions and units
 */

import { Unit, PaperSize } from '../types';

export const ARCH_PAPER_SIZES = {
  ARCH_A: {
    width: 12,
    height: 9,
    unit: 'inch' as const,
  },
  ARCH_B: {
    width: 18,
    height: 12,
    unit: 'inch' as const,
  },
  ARCH_C: {
    width: 24,
    height: 18,
    unit: 'inch' as const,
  },
  ARCH_D: {
    width: 36,
    height: 24,
    unit: 'inch' as const,
  },
  ARCH_E1: {
    width: 42,
    height: 30,
    unit: 'inch' as const,
  },
  ARCH_E: {
    width: 48,
    height: 36,
    unit: 'inch' as const,
  },
} satisfies Record<string, PaperSize>;

// Export default paper size for backward compatibility
export const DEFAULT_PAPER_SIZE = ARCH_PAPER_SIZES.ARCH_D;

// Fixed reference dimensions to ensure consistent measurements regardless of viewport changes
export const REFERENCE_CANVAS_DIMENSIONS = {
  width: 900,
  height: 500,
};
