/**
 * Component-related type definitions
 */

import { GeometryType, SelectionType } from './shared';

export interface Component {
  id: number;
  name: string;
  selectionType: SelectionType;
  blueprintFileId: string;
  geometryType: GeometryType;
  blueprintImageId?: string; // Optional, present if selectionType is 'currentPage'
  description?: string; // Optional
  color: string;
  shade: string;
}

export interface CreateComponentPayload {
  name: string;
  selectionType: SelectionType;
  blueprintFileId: string;
  geometryType: GeometryType;
  blueprintImageId?: string;
  description?: string;
  color: string;
  shade: string;
}

export type UpdateComponentPayload = Partial<CreateComponentPayload> & {
  id: number;
};

export interface GetComponentsParams {
  blueprintFileId: string;
  blueprintImageId?: string;
  activeTab: 'aggregate' | 'specific';
  search?: string;
}

export interface ImageType {
  id: string;
  name: string;
  url: string;
}
