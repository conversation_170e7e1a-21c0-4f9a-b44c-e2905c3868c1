/**
 * Drawing-related type definitions
 */

// Simplified inline type for the 'takeoff' object nested within blueprintFile
interface TakeoffInfoForDrawing {
  id: number;
  userId: string;
  name: string;
}

// Simplified inline type for the 'blueprintFile' object nested within blueprintImage
interface BlueprintFileInfoForDrawing {
  id: string; // UUID
  fileName: string;
  takeoff: TakeoffInfoForDrawing;
}

// Simplified inline type for the 'blueprintImage' object nested within Drawing
interface BlueprintImageDetailsForDrawing {
  id: string; // UUID
  filename: string;
  blueprintFile: BlueprintFileInfoForDrawing;
}

// Simplified inline type for the 'component' object nested within Drawing
interface ComponentDetailsForDrawing {
  id: number;
  name: string;
  color: string;
  shade: string;
}

/**
 * Represents the data structure for a drawing object,
 * as returned by the GET /drawings endpoint.
 */
export interface Drawing {
  id: number;
  blueprintImageId: string;
  componentId: number | null;
  config: Record<string, string>;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  blueprintImage?: BlueprintImageDetailsForDrawing;
  component?: ComponentDetailsForDrawing | null;
}

/**
 * Defines the payload structure for creating a new drawing
 * via the POST /drawings endpoint.
 */
export interface CreateDrawingPayload {
  blueprintImageId: string;
  componentId?: number;
  config: Record<string, string>;
  _optimisticComponent?: ComponentDetailsForDrawing | null;
  skipOptimisticUpdate?: boolean;
}

/**
 * Defines the parameters for fetching drawings
 * via the GET /drawings endpoint.
 */
export interface GetDrawingsParams {
  blueprintImageId: string;
}

/**
 * Defines the payload structure for updating an existing drawing
 * via the PATCH /drawings/:drawingId endpoint.
 */
export interface UpdateDrawingPayload {
  drawingId: number;
  blueprintImageId: string;
  componentId?: number;
  config: Record<string, string>;
  skipOptimisticUpdate?: boolean;
}

/**
 * Defines the response structure for updating a drawing.
 */
export interface UpdateDrawingResponse {
  message: string;
  data: Drawing;
}
