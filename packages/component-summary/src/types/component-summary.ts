/**
 * Component summary-related type definitions
 */

import { GeometryType } from './shared';
import { Drawing } from './drawing';
import {
  ComponentTotalMetrics,
  DrawingSummaryItem,
  ScaleInfo,
  PaperSize,
} from './measurement';

export interface ComponentSummaryData {
  componentId: number;
  componentName: string;
  componentType: GeometryType;
  totalMetrics: ComponentTotalMetrics;
  drawings: Drawing[]; // Full Drawing objects for tooltip
  drawingSummaryItems: DrawingSummaryItem[]; // Summary items for display
  isExpanded: boolean;
}

export interface ComponentSummaryState {
  expandedComponentIds: Set<number>;
  summaryData: Map<number, ComponentSummaryData>;
}

export interface ProjectTotals {
  totalSurfaceArea: ComponentTotalMetrics;
  totalEdgeLength: ComponentTotalMetrics;
  totalPointCount: ComponentTotalMetrics;
}

export interface ComponentSummaryExport {
  takeoffInfo: {
    id: string;
    name: string;
    exportType: 'aggregate' | 'specific';
    blueprintImageId?: string;
    exportedAt: string;
  };
  scale: ScaleInfo;
  paperSize: PaperSize;
  components: ComponentSummaryData[];
  totals: ProjectTotals;
}
