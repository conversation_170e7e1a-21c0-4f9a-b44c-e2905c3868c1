/**
 * Measurement-related type definitions
 */

import { Unit } from './shared';

export interface ScaleInfo {
  num_metric: number;
  num_unit: Unit;
  den_metric: number;
  den_unit: Unit;
}

export interface PaperSize {
  width: number;
  height: number;
  unit: Unit;
}

export interface DimensionInfo {
  width: number;
  height: number;
  unit: Unit;
}

export interface DistanceMeasurement {
  pixels: number;
  realWorld: number;
  unit: string;
  formatted: string;
}

export interface DynamicMeasurement {
  area?: {
    value: number;
    unit: string;
    formatted: string;
  };
  length?: {
    value: number;
    unit: string;
    formatted: string;
  };
  perimeter?: {
    value: number;
    unit: string;
    formatted: string;
  };
  circumference?: {
    value: number;
    unit: string;
    formatted: string;
  };
}

export interface ComponentTotalMetrics {
  type: 'area' | 'length' | 'count';
  value: number;
  formattedValue: string;
  unit: string;
}

export interface DrawingSummaryItem {
  id: number;
  displayId: string; // "Surface ID 123", "Edge ID 456", "Point ID 789"
  measurement: {
    value: number;
    formattedValue: string;
    unit: string;
  } | null; // null for point drawings
}
